"""
CTM Service Module

This module provides a service class for interacting with Thales CipherTrust Manager (CTM)
to manage cryptographic keys. It handles authentication, key upload, key existence checks,
and other CTM operations.

The service supports:
- CTM authentication and token management
- Key upload with various algorithms (AES, RSA, HMAC)
- Key existence verification
- Batch key operations
- Key metadata and permissions management

All operations include proper error handling and response formatting.
"""

import base64
from typing import Dict, Any, List, Optional
from sq_ctm_modules_v1 import ctm_upload_key, ctm_upload_key_full_response, ctm_get_api_key, ctm_key_exists


class CTMService:
    """
    Service class for Thales CipherTrust Manager (CTM) operations.

    This service provides methods to interact with the CTM API for key management
    operations including authentication, key upload, and key verification.
    It maintains authentication state and handles API communication.

    Attributes:
        base_url (str): The base URL of the CTM server
        username (str): CTM username for authentication
        password (str): CTM password for authentication
        domain (str): CTM domain for authentication
        _api_key (str): Cached API authentication token
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize CTM service with configuration.

        Args:
            config (Dict[str, Any]): CTM configuration dictionary containing:
                - base_url (str): CTM server URL
                - username (str): CTM username
                - password (str): CTM password
                - domain (str): CTM domain

        Raises:
            KeyError: If required configuration keys are missing
        """
        self.base_url = config['base_url']
        self.username = config['username']
        self.password = config['password']
        self.domain = config['domain']
        self._api_key = None
    
    def _get_login_data(self) -> Dict[str, str]:
        """
        Get login data dictionary for CTM authentication
        
        Returns:
            Login data dictionary
        """
        return {
            "name": self.username,
            "password": self.password,
            "domain": self.domain
        }
    
    def get_api_key(self) -> str:
        """
        Get CTM API authentication token
        
        Returns:
            API key string
        """
        login_data = self._get_login_data()
        api_key = ctm_get_api_key(self.base_url, login_data)
        self._api_key = api_key
        return api_key
    
    def check_key_exists(self, key_name: str) -> bool:
        """
        Check if a key exists in CTM
        
        Args:
            key_name: Name of the key to check
            
        Returns:
            True if key exists, False otherwise
        """
        if not self._api_key:
            self.get_api_key()
        
        return ctm_key_exists(self.base_url, self._api_key, key_name)
    
    def upload_key(self, key_name: str, key_material: bytes, algorithm: str,
                   owner: str, unexportable: bool = True, additional_meta: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Upload a key to CTM with enhanced RSA support

        This method now provides enhanced support for RSA keys by:
        1. Using the full CTM response function for RSA keys to capture complete metadata
        2. Returning RSA-specific fields like publickey, objectType, size, links
        3. Maintaining backward compatibility for other algorithms (AES, HMAC)

        Args:
            key_name: Name for the key
            key_material: Raw key material bytes (PEM format for RSA, raw bytes for others)
            algorithm: Key algorithm (RSA, AES, HMAC, etc.)
            owner: Key owner
            unexportable: Whether key should be unexportable
            additional_meta: Additional metadata to include in the key

        Returns:
            Upload result dictionary with enhanced RSA fields:
            - Standard fields: key_name, algorithm, num_bytes, owner, etc.
            - RSA-specific fields: ctm_public_key, ctm_links, ctm_object_type, ctm_response
        """
        if not self._api_key:
            self.get_api_key()

        # Enhanced RSA key handling: Use full response function to capture complete CTM metadata
        if algorithm.upper() == 'RSA':
            # For RSA keys, use the enhanced upload function that returns complete CTM response
            # This captures RSA-specific fields like publickey, objectType, size, links, etc.
            ctm_response = ctm_upload_key_full_response(
                self.base_url,
                self._api_key,
                key_name,
                key_material,
                algorithm,
                owner,
                unexportable,
                additional_meta
            )

            # Build enhanced response with RSA-specific fields from CTM
            result = {
                "key_name": key_name,
                "algorithm": algorithm,
                "num_bytes": len(key_material),
                "owner": owner,
                "unexportable": unexportable,
                "upload_successful": True,
                "key_material_base64": base64.b64encode(key_material).decode('utf-8'),
                "ctm_key_id": ctm_response.get('id'),
                "ctm_response": ctm_response  # Complete CTM response for debugging/analysis
            }

            # Extract and include RSA-specific fields from CTM response
            if 'publickey' in ctm_response:
                result["ctm_public_key"] = ctm_response['publickey']
            if 'links' in ctm_response:
                result["ctm_links"] = ctm_response['links']
            if 'size' in ctm_response:
                result["ctm_key_size"] = ctm_response['size']
            if 'objectType' in ctm_response:
                result["ctm_object_type"] = ctm_response['objectType']

            return result
        else:
            # For non-RSA keys, use the original function
            ctm_key_id = ctm_upload_key(
                self.base_url,
                self._api_key,
                key_name,
                key_material,
                algorithm,
                owner,
                unexportable,
                additional_meta
            )

            return {
                "key_name": key_name,
                "algorithm": algorithm,
                "num_bytes": len(key_material),
                "owner": owner,
                "unexportable": unexportable,
                "upload_successful": True,
                "key_material_base64": base64.b64encode(key_material).decode('utf-8'),
                "ctm_key_id": ctm_key_id
            }
    
    def upload_key_with_provided_material(self, key_name: str, key_material_b64: str,
                                        algorithm: str, owner: str, unexportable: bool = True,
                                        additional_meta: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Upload a key to CTM using provided base64 key material

        Args:
            key_name: Name for the key
            key_material_b64: Base64 encoded key material
            algorithm: Key algorithm
            owner: Key owner
            unexportable: Whether key should be unexportable
            additional_meta: Additional metadata to include in the key

        Returns:
            Upload result dictionary
        """
        # Decode base64 key material
        key_material = base64.b64decode(key_material_b64)

        return self.upload_key(key_name, key_material, algorithm, owner, unexportable, additional_meta)
    
    def upload_multiple_keys(self, key_base_name: str, key_count: int, key_material_list: List[bytes],
                           algorithm: str, owner: str, unexportable: bool = True,
                           additional_meta: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Upload multiple keys to CTM

        Args:
            key_base_name: Base name for keys (will be appended with numbers)
            key_count: Number of keys to upload
            key_material_list: List of key material bytes
            algorithm: Key algorithm
            owner: Key owner
            unexportable: Whether keys should be unexportable
            additional_meta: Additional metadata to include in all keys

        Returns:
            Batch upload result dictionary
        """
        if not self._api_key:
            self.get_api_key()
        
        uploaded_keys = []
        failed_keys = []
        
        for i in range(key_count):
            key_name = f"{key_base_name}_{i+1:03d}"
            
            try:
                # Check if key already exists
                if self.check_key_exists(key_name):
                    failed_keys.append({
                        "key_name": key_name,
                        "reason": "Key already exists"
                    })
                    continue
                
                # Upload key
                result = self.upload_key(
                    key_name,
                    key_material_list[i],
                    algorithm,
                    owner,
                    unexportable,
                    additional_meta
                )
                uploaded_keys.append(result)
                
            except Exception as e:
                failed_keys.append({
                    "key_name": key_name,
                    "reason": str(e)
                })
        
        return {
            "uploaded_count": len(uploaded_keys),
            "failed_count": len(failed_keys),
            "total_requested": key_count,
            "uploaded_keys": uploaded_keys,
            "failed_keys": failed_keys,
            "algorithm": algorithm,
            "owner": owner,
            "unexportable": unexportable
        }
