"""
CTM Controller Module

This module provides REST API endpoints for Thales CipherTrust Manager (CTM)
integration and key management operations.

Supported operations:
- CTM authentication and token management
- Key existence verification in CTM
- Single key upload to CTM with quantum-generated material
- Batch key upload operations
- Dynamic configuration support for CTM and SeQRNG
- RSA key pair generation and upload
- Key metadata and permissions management

The controller supports both static configuration (from environment/config files)
and dynamic configuration (provided in request payload) for maximum flexibility
in different deployment scenarios.
"""

import base64
from flask import Blueprint, request, current_app
from app.utils.response_helpers import create_success_response, create_error_response
from app.utils.validators import validate_json_request
from app.services.ctm_service import CTMService
from app.services.seqrng_service import SeQRNGService
from app.services.validation_service import ValidationService
from app.config.settings import parse_connection_string
from sq_ctm_modules_v1 import group_bytes

ctm_bp = Blueprint('ctm', __name__)


def get_ctm_service():
    """Get CTM service instance"""
    ctm_config = current_app.config.get('CTM_CONFIG')
    if not ctm_config:
        raise Exception("CTM configuration not loaded")
    return CTMService(ctm_config)


def get_seqrng_service(seqrng_config=None):
    """Get SeQRNG service instance with optional dynamic configuration"""
    if seqrng_config:
        # Normalize dynamic configuration to match expected format
        normalized_config = normalize_seqrng_config(seqrng_config)
        return SeQRNGService(normalized_config)
    else:
        # Use global configuration
        global_seqrng_config = current_app.config.get('SEQRNG_CONFIG')
        if not global_seqrng_config:
            raise Exception("SeQRNG configuration not loaded")
        return SeQRNGService(global_seqrng_config)


def validate_seqrng_config(seqrng_config):
    """Validate SeqRNG configuration"""
    if not seqrng_config:
        return None

    required_seqrng_fields = ['ip_address', 'api_token']
    for field in required_seqrng_fields:
        if not seqrng_config.get(field):
            return f"SeqRNG configuration missing required field: {field}"

    return None


def normalize_seqrng_config(seqrng_config):
    """
    Normalize SeqRNG configuration to include base_url

    Args:
        seqrng_config: SeqRNG configuration dictionary with ip_address

    Returns:
        Normalized SeqRNG configuration with base_url
    """
    normalized_config = seqrng_config.copy()

    # Convert ip_address to base_url if not already present
    if 'base_url' not in normalized_config and 'ip_address' in normalized_config:
        try:
            normalized_config['base_url'] = parse_connection_string(
                normalized_config['ip_address'],
                default_protocol="https"
            )
        except ValueError as e:
            raise ValueError(f"Invalid SeqRNG IP address format '{normalized_config['ip_address']}': {e}")

    return normalized_config


def normalize_ctm_config(ctm_config):
    """
    Normalize CTM configuration to include base_url

    Args:
        ctm_config: CTM configuration dictionary with ip_address

    Returns:
        Normalized CTM configuration with base_url
    """
    normalized_config = ctm_config.copy()

    # Convert ip_address to base_url if not already present
    if 'base_url' not in normalized_config and 'ip_address' in normalized_config:
        try:
            normalized_config['base_url'] = parse_connection_string(
                normalized_config['ip_address'],
                default_protocol="https"
            )
        except ValueError as e:
            raise ValueError(f"Invalid CTM IP address format '{normalized_config['ip_address']}': {e}")

    return normalized_config


@ctm_bp.route('/api/v1/ctm/auth/token', methods=['GET'])
def get_ctm_token():
    """Get CTM API token"""
    try:
        ctm_service = get_ctm_service()
        api_key = ctm_service.get_api_key()

        response_data = {
            "token_obtained": True,
            "ctm_url": ctm_service.base_url
        }

        return create_success_response(response_data, "CTM token obtained successfully")

    except Exception as e:
        return create_error_response(f"Failed to get CTM token: {str(e)}", 500, "authentication_error")


@ctm_bp.route('/api/v1/ctm/keys/<key_name>/exists', methods=['GET'])
def check_key_exists(key_name):
    """Check if a key exists in CTM"""
    try:
        ctm_service = get_ctm_service()
        exists = ctm_service.check_key_exists(key_name)

        response_data = {
            "key_name": key_name,
            "exists": exists
        }

        return create_success_response(response_data, f"Key existence check completed")

    except Exception as e:
        return create_error_response(f"Failed to check key existence: {str(e)}", 500, "check_error")


@ctm_bp.route('/api/v1/ctm/keys/upload', methods=['POST'])
def upload_key_to_ctm():
    """Upload a single key to CTM"""
    try:
        # Validate JSON request
        json_error = validate_json_request()
        if json_error:
            return json_error

        # Get request data
        data = request.get_json()
        key_name = data.get('key_name')
        algorithm = data.get('algorithm')
        num_bytes = data.get('num_bytes')
        owner = data.get('owner')
        exportable = data.get('exportable', False)
        key_material_b64 = data.get('key_material_base64')

        # Validate request
        validation_errors = ValidationService.validate_key_upload_request(
            key_name, algorithm, num_bytes, owner, exportable
        )
        if validation_errors:
            return create_error_response("; ".join(validation_errors), 400, "validation_error")

        # Get services
        ctm_service = get_ctm_service()
        seqrng_service = get_seqrng_service()

        # Check if key already exists
        if ctm_service.check_key_exists(key_name):
            return create_error_response(f"Key '{key_name}' already exists in CTM", 409, "conflict_error")

        # Prepare additional metadata
        additional_meta = {
            "cte": {
                "is_used": True,
                "cte_versioned": True,
                "encryption_mode": "CBC",
                "unique_to_client": False,
                "persistent_on_client": True,
                "unique_to_client_format": ""
            },
            "permissions": {
                "ReadKey": [
                    "CTE Clients"
                ],
                "ExportKey": [
                    "CTE Clients"
                ]
            }
        }

        # Get key material
        if key_material_b64:
            # Use provided key material
            entropy_report = {"source": "provided", "quantum_fidelity": "N/A", "entropy_string": "N/A", "entropy_status": "N/A"}
            result = ctm_service.upload_key_with_provided_material(
                key_name, key_material_b64, algorithm, owner, not exportable, additional_meta
            )
        else:
            # Check if algorithm is RSA
            if algorithm and algorithm.upper() == 'RSA':
                # Generate RSA key pair instead of raw key material
                # Handle both bits and bytes input for RSA
                if num_bytes in [1024, 2048, 3072, 4096]:
                    # Input is in bits
                    key_size = num_bytes
                elif num_bytes in [128, 256, 384, 512]:
                    # Input is in bytes, convert to bits
                    key_size = num_bytes * 8
                else:
                    # Default to 2048 bits if invalid size
                    key_size = 2048

                rsa_result = seqrng_service.generate_rsa_key_pair(key_size)
                entropy_report = rsa_result["entropy_report"]

                # Enhanced RSA handling: Upload private key in PEM format
                # CTM expects RSA keys in PEM format (PEM-encoded ASN.1 DER-encoded PKCS #1)
                private_key_pem = rsa_result["private_key"]
                private_key_bytes = private_key_pem.encode('utf-8')
                result = ctm_service.upload_key(
                    key_name, private_key_bytes, algorithm, owner, not exportable, additional_meta
                )

                # Add RSA-specific information to response
                result["rsa_public_key"] = rsa_result["public_key"]
                result["key_size"] = key_size
            else:
                # Generate new key material from SeQRNG (AES and other algorithms)
                key_material, entropy_report = seqrng_service.generate_key_material(num_bytes)
                result = ctm_service.upload_key(
                    key_name, key_material, algorithm, owner, not exportable, additional_meta
                )

        # Prepare response
        response_data = {
            "key_name": result["key_name"],
            "algorithm": result["algorithm"],
            "num_bytes": result["num_bytes"],
            "owner": result["owner"],
            "exportable": exportable,
            "upload_successful": result["upload_successful"],
            "entropy_report": entropy_report,
            "key_material_base64": result["key_material_base64"],
            "ctm_key_id": result.get("ctm_key_id")
        }

        # Enhanced RSA response: Include both SeQRNG and CTM RSA-specific fields
        if algorithm and algorithm.upper() == 'RSA':
            # SeQRNG-generated RSA fields
            response_data["rsa_public_key"] = result.get("rsa_public_key")
            response_data["key_size"] = result.get("key_size")

            # CTM-specific RSA fields (new enhancement)
            # These fields provide complete CTM metadata for RSA keys
            if "ctm_public_key" in result:
                response_data["ctm_public_key"] = result["ctm_public_key"]
            if "ctm_links" in result:
                response_data["ctm_links"] = result["ctm_links"]
            if "ctm_key_size" in result:
                response_data["ctm_key_size"] = result["ctm_key_size"]
            if "ctm_object_type" in result:
                response_data["ctm_object_type"] = result["ctm_object_type"]
            if "ctm_response" in result:
                response_data["ctm_full_response"] = result["ctm_response"]

        return create_success_response(response_data, f"Key '{key_name}' uploaded successfully to CTM")

    except Exception as e:
        return create_error_response(f"Failed to upload key to CTM: {str(e)}", 500, "upload_error")


@ctm_bp.route('/api/v1/ctm/keys/upload/batch', methods=['POST'])
def upload_batch_keys_to_ctm():
    """Upload multiple keys to CTM"""
    try:
        # Validate JSON request
        json_error = validate_json_request()
        if json_error:
            return json_error

        # Get request data
        data = request.get_json()
        key_base_name = data.get('key_base_name')
        algorithm = data.get('algorithm')
        num_bytes = data.get('num_bytes')
        key_count = data.get('key_count')
        owner = data.get('owner')
        exportable = data.get('exportable', False)

        # Validate request
        validation_errors = ValidationService.validate_batch_upload_request(
            key_base_name, algorithm, num_bytes, key_count, owner, exportable
        )
        if validation_errors:
            return create_error_response("; ".join(validation_errors), 400, "validation_error")

        # Get services
        ctm_service = get_ctm_service()
        seqrng_service = get_seqrng_service()

        # Generate key material for all keys
        print(f"🔥 Generating {key_count} keys of {num_bytes} bytes each...")
        key_material_list = []
        entropy_reports = []

        for i in range(key_count):
            key_material, entropy_report = seqrng_service.generate_key_material(num_bytes)
            key_material_list.append(key_material)
            entropy_reports.append(entropy_report)

        # Prepare additional metadata
        additional_meta = {
            "cte": {
                "is_used": True,
                "cte_versioned": True,
                "encryption_mode": "CBC",
                "unique_to_client": False,
                "persistent_on_client": True,
                "unique_to_client_format": ""
            },
            "permissions": {
                "ReadKey": [
                    "CTE Clients"
                ],
                "ExportKey": [
                    "CTE Clients"
                ]
            }
        }

        # Upload keys to CTM
        result = ctm_service.upload_multiple_keys(
            key_base_name, key_count, key_material_list, algorithm, owner, not exportable, additional_meta
        )

        # Prepare response
        response_data = {
            "key_base_name": key_base_name,
            "algorithm": result["algorithm"],
            "num_bytes": num_bytes,
            "key_count": key_count,
            "owner": result["owner"],
            "exportable": exportable,
            "uploaded_count": result["uploaded_count"],
            "failed_count": result["failed_count"],
            "total_requested": result["total_requested"],
            "uploaded_keys": result["uploaded_keys"],
            "failed_keys": result["failed_keys"],
            "entropy_reports": entropy_reports
        }

        message = f"Batch upload completed: {result['uploaded_count']}/{result['total_requested']} keys uploaded successfully"
        return create_success_response(response_data, message)

    except Exception as e:
        return create_error_response(f"Failed to upload batch keys to CTM: {str(e)}", 500, "batch_upload_error")


# Dynamic CTM and SeqRNG Configuration Endpoints

@ctm_bp.route('/api/v1/ctm/keys/upload/dynamic', methods=['POST'])
def upload_key_to_ctm_dynamic():
    """Upload a single key to CTM with dynamic CTM and SeqRNG configuration"""
    try:
        # Get request data
        data = request.get_json() or {}
        key_name = data.get('key_name')
        algorithm = data.get('algorithm')
        num_bytes = data.get('num_bytes')
        owner = data.get('owner')
        exportable = data.get('exportable', False)
        key_material_b64 = data.get('key_material_base64')
        ctm_config = data.get('ctm_config')
        seqrng_config = data.get('seqrng_config')

        print(data)

        # Validate CTM configuration
        if not ctm_config:
            return create_error_response("CTM configuration is required for dynamic endpoints", 400, "validation_error")

        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Validate SeqRNG configuration if provided
        seqrng_error = validate_seqrng_config(seqrng_config)
        if seqrng_error:
            return create_error_response(seqrng_error, 400, "validation_error")

        # Validate request
        validation_errors = ValidationService.validate_key_upload_request(
            key_name, algorithm, num_bytes, owner, exportable
        )
        if validation_errors:
            return create_error_response("; ".join(validation_errors), 400, "validation_error")

        # Normalize and create services with dynamic config
        normalized_ctm_config = normalize_ctm_config(ctm_config)
        ctm_service = CTMService(normalized_ctm_config)
        seqrng_service = get_seqrng_service(seqrng_config)

        # Generate key material if not provided
        if not key_material_b64:
            # Check if algorithm is RSA
            if algorithm and algorithm.upper() == 'RSA':
                # Generate RSA key pair instead of raw key material
                # Handle both bits and bytes input for RSA
                if num_bytes in [1024, 2048, 3072, 4096]:
                    # Input is in bits
                    key_size = num_bytes
                elif num_bytes in [128, 256, 384, 512]:
                    # Input is in bytes, convert to bits
                    key_size = num_bytes * 8
                else:
                    # Default to 2048 bits if invalid size
                    key_size = 2048

                rsa_result = seqrng_service.generate_rsa_key_pair(key_size)
                entropy_report = rsa_result["entropy_report"]

                # Enhanced RSA handling: Use PEM format for CTM upload
                # CTM expects RSA keys in PEM format (PEM-encoded ASN.1 DER-encoded PKCS #1)
                private_key_pem = rsa_result["private_key"]
                key_material = private_key_pem.encode('utf-8')
                key_material_b64 = base64.b64encode(key_material).decode('utf-8')

                # Store RSA-specific data for response
                rsa_public_key = rsa_result["public_key"]
                rsa_key_size = key_size
            else:
                # Generate new key material from SeQRNG (AES and other algorithms)
                key_material, entropy_report = seqrng_service.generate_key_material(num_bytes)
                key_material_b64 = base64.b64encode(key_material).decode('utf-8')
                rsa_public_key = None
                rsa_key_size = None
        else:
            # Validate provided key material
            try:
                key_material = base64.b64decode(key_material_b64)
                if len(key_material) != num_bytes:
                    return create_error_response(f"Key material length ({len(key_material)}) doesn't match num_bytes ({num_bytes})", 400, "validation_error")
                entropy_report = {"entropy_status": "provided", "entropy_string": "N/A", "quantum_fidelity": "N/A"}
                rsa_public_key = None
                rsa_key_size = None
            except Exception as e:
                return create_error_response(f"Invalid base64 key material: {str(e)}", 400, "validation_error")

        # Prepare additional metadata
        additional_meta = {
            "cte": {
                "is_used": True,
                "cte_versioned": True,
                "encryption_mode": "CBC",
                "unique_to_client": False,
                "persistent_on_client": True,
                "unique_to_client_format": ""
            },
            "permissions": {
                "ReadKey": ["CTE Clients"],
                "ExportKey": ["CTE Clients"]
            }
        }

        # Upload key to CTM
        result = ctm_service.upload_key(
            key_name, key_material, algorithm, owner, not exportable, additional_meta
        )

        # Prepare response
        response_data = {
            "key_name": result["key_name"],
            "algorithm": result["algorithm"],
            "num_bytes": num_bytes,
            "owner": result["owner"],
            "exportable": exportable,
            "entropy_report": entropy_report,
            "ctm_key_id": result.get("ctm_key_id")
        }

        # Enhanced RSA response: Include both SeQRNG and CTM RSA-specific fields
        if algorithm and algorithm.upper() == 'RSA':
            # SeQRNG-generated RSA fields
            response_data["rsa_public_key"] = rsa_public_key
            response_data["key_size"] = rsa_key_size

            # CTM-specific RSA fields (new enhancement)
            # These fields provide complete CTM metadata for RSA keys
            if "ctm_public_key" in result:
                response_data["ctm_public_key"] = result["ctm_public_key"]
            if "ctm_links" in result:
                response_data["ctm_links"] = result["ctm_links"]
            if "ctm_key_size" in result:
                response_data["ctm_key_size"] = result["ctm_key_size"]
            if "ctm_object_type" in result:
                response_data["ctm_object_type"] = result["ctm_object_type"]
            if "ctm_response" in result:
                response_data["ctm_full_response"] = result["ctm_response"]

        return create_success_response(response_data, "Key uploaded successfully to CTM")

    except Exception as e:
        print(f"Error in upload_key_to_ctm_dynamic: {str(e)}")
        import traceback
        traceback.print_exc()
        return create_error_response(f"Failed to upload key to CTM: {str(e)}", 500, "upload_error")


@ctm_bp.route('/api/v1/ctm/keys/upload/batch/dynamic', methods=['POST'])
def upload_batch_keys_to_ctm_dynamic():
    """Upload multiple keys to CTM with dynamic CTM and SeqRNG configuration"""
    try:
        # Get request data
        data = request.get_json() or {}
        key_base_name = data.get('key_base_name')
        algorithm = data.get('algorithm')
        num_bytes = data.get('num_bytes')
        key_count = data.get('key_count')
        owner = data.get('owner')
        exportable = data.get('exportable', False)
        ctm_config = data.get('ctm_config')
        seqrng_config = data.get('seqrng_config')

        # Validate CTM configuration
        if not ctm_config:
            return create_error_response("CTM configuration is required for dynamic endpoints", 400, "validation_error")

        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Validate SeqRNG configuration if provided
        seqrng_error = validate_seqrng_config(seqrng_config)
        if seqrng_error:
            return create_error_response(seqrng_error, 400, "validation_error")

        # Validate request
        validation_errors = ValidationService.validate_batch_upload_request(
            key_base_name, algorithm, num_bytes, key_count, owner, exportable
        )
        if validation_errors:
            return create_error_response("; ".join(validation_errors), 400, "validation_error")

        # Normalize and create services with dynamic config
        normalized_ctm_config = normalize_ctm_config(ctm_config)
        ctm_service = CTMService(normalized_ctm_config)
        seqrng_service = get_seqrng_service(seqrng_config)

        # Generate key material for all keys
        key_material_list = []
        entropy_reports = []

        for i in range(key_count):
            key_material, entropy_report = seqrng_service.generate_key_material(num_bytes)
            key_material_list.append(key_material)
            entropy_reports.append(entropy_report)

        # Prepare additional metadata
        additional_meta = {
            "cte": {
                "is_used": True,
                "cte_versioned": True,
                "encryption_mode": "CBC",
                "unique_to_client": False,
                "persistent_on_client": True,
                "unique_to_client_format": ""
            },
            "permissions": {
                "ReadKey": ["CTE Clients"],
                "ExportKey": ["CTE Clients"]
            }
        }

        # Upload keys to CTM
        result = ctm_service.upload_multiple_keys(
            key_base_name, key_count, key_material_list, algorithm, owner, not exportable, additional_meta
        )

        # Prepare response
        response_data = {
            "key_base_name": key_base_name,
            "algorithm": result["algorithm"],
            "num_bytes": num_bytes,
            "key_count": key_count,
            "owner": result["owner"],
            "exportable": exportable,
            "uploaded_count": result["uploaded_count"],
            "failed_count": result["failed_count"],
            "total_requested": result["total_requested"],
            "uploaded_keys": result["uploaded_keys"],
            "failed_keys": result["failed_keys"],
            "entropy_reports": entropy_reports
        }

        message = f"Batch upload completed: {result['uploaded_count']}/{result['total_requested']} keys uploaded successfully"
        return create_success_response(response_data, message)

    except Exception as e:
        return create_error_response(f"Failed to upload batch keys to CTM: {str(e)}", 500, "batch_upload_error")


@ctm_bp.route('/api/v1/ctm/keys/<key_name>/exists/dynamic', methods=['POST'])
def check_key_exists_dynamic(key_name):
    """Check if a key exists in CTM with dynamic configuration"""
    try:
        # Get request data
        data = request.get_json() or {}
        ctm_config = data.get('ctm_config')

        # Validate CTM configuration
        if not ctm_config:
            return create_error_response("CTM configuration is required for dynamic endpoints", 400, "validation_error")

        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Validate key name
        if not key_name or not key_name.strip():
            return create_error_response("Key name is required", 400, "validation_error")

        # Normalize and create CTM service with dynamic config
        normalized_ctm_config = normalize_ctm_config(ctm_config)
        ctm_service = CTMService(normalized_ctm_config)

        # Check if key exists
        exists = ctm_service.check_key_exists(key_name.strip())

        response_data = {
            "key_name": key_name.strip(),
            "exists": exists
        }

        message = f"Key '{key_name}' {'exists' if exists else 'does not exist'} in CTM"
        return create_success_response(response_data, message)

    except Exception as e:
        return create_error_response(f"Failed to check key existence: {str(e)}", 500, "check_error")


@ctm_bp.route('/api/v1/ctm/auth/token/dynamic', methods=['POST'])
def get_ctm_token_dynamic():
    """Get CTM authentication token with dynamic configuration"""
    try:
        # Get request data
        data = request.get_json() or {}
        ctm_config = data.get('ctm_config')

        # Validate CTM configuration
        if not ctm_config:
            return create_error_response("CTM configuration is required for dynamic endpoints", 400, "validation_error")

        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Normalize and create CTM service with dynamic config
        normalized_ctm_config = normalize_ctm_config(ctm_config)
        ctm_service = CTMService(normalized_ctm_config)

        # Get authentication token
        token_info = ctm_service.get_auth_token()

        response_data = {
            "token": token_info.get("jwt"),
            "expires_in": token_info.get("duration"),
            "token_type": "Bearer"
        }

        return create_success_response(response_data, "CTM authentication token obtained successfully")

    except Exception as e:
        return create_error_response(f"Failed to get CTM token: {str(e)}", 500, "auth_error")


@ctm_bp.route('/api/v1/ctm/keys/<key_id>/versions', methods=['POST'])
def create_key_version(key_id):
    """Create a new version of an existing key with quantum material"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        ctm_config = data.get('ctm_config')
        seqrng_config = data.get('seqrng_config')
        archive_previous = data.get('archive_previous', False)

        # Validate required parameters
        if not key_id:
            return create_error_response("Key ID is required", 400, "validation_error")

        if not ctm_config:
            return create_error_response("CTM configuration is required", 400, "validation_error")

        # Validate CTM configuration
        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Validate SeqRNG configuration if provided
        seqrng_error = validate_seqrng_config(seqrng_config)
        if seqrng_error:
            return create_error_response(seqrng_error, 400, "validation_error")

        # Normalize and create services with dynamic config
        normalized_ctm_config = normalize_ctm_config(ctm_config)
        ctm_service = CTMService(normalized_ctm_config)
        seqrng_service = get_seqrng_service(seqrng_config)

        # Get key information first
        print(f"🔍 Getting information for key ID: {key_id}")
        key_info = ctm_service.get_key_info(key_id)

        if not key_info:
            return create_error_response(f"Key with ID '{key_id}' not found or inaccessible", 404, "key_not_found")

        # Extract key information
        algorithm = key_info.get('algorithm', 'aes').lower()
        current_size = key_info.get('size')
        key_name = key_info.get('name', 'unknown')

        print(f"📋 Key information:")
        print(f"   🔑 Name: {key_name}")
        print(f"   📊 Algorithm: {algorithm}")
        print(f"   🔢 Current size: {current_size}")

        # Determine number of bytes needed based on current key size
        if algorithm == 'rsa':
            # For RSA, we need to handle key size differently
            if current_size and isinstance(current_size, (int, str)):
                try:
                    current_size_int = int(current_size)
                    # RSA key size is in bits, convert to bytes for material generation
                    numbytes = current_size_int // 8
                except ValueError:
                    numbytes = 256  # Default for 2048-bit RSA
            else:
                numbytes = 256  # Default for 2048-bit RSA
        else:
            # For symmetric algorithms (AES, ARIA, HMAC)
            if current_size and isinstance(current_size, (int, str)):
                try:
                    current_size_int = int(current_size)
                    numbytes = current_size_int // 8
                except ValueError:
                    numbytes = 32  # Default
            else:
                numbytes = 32

        print(f"🔢 Will generate {numbytes} bytes of quantum material")

        # Generate quantum random bytes from SeQRNG
        print(f"🔥 Generating quantum material from SeQRNG...")
        key_material, entropy_report = seqrng_service.generate_key_material(numbytes)

        if not key_material or len(key_material) != numbytes:
            return create_error_response("Failed to generate quantum material with correct length", 500, "generation_error")

        print(f"✅ Generated {len(key_material)} bytes of quantum material")

        # Create new version with quantum material
        print(f"🔄 Creating new version in CTM...")
        version_result = ctm_service.create_key_version(key_id, key_material, algorithm)

        if not version_result.get('success'):
            return create_error_response(f"Failed to create new version: {version_result.get('error')}", 500, "version_creation_error")

        # Optionally archive the previous version
        archive_success = True
        if archive_previous:
            print(f"📦 Archiving previous version...")
            archive_success = ctm_service.archive_key(key_id)
            if not archive_success:
                print(f"⚠️ Warning: Failed to archive previous version, but new version was created successfully")

        # Prepare response
        response_data = {
            "key_id": key_id,
            "key_name": key_name,
            "algorithm": algorithm,
            "version_id": version_result.get('version_id'),
            "version_number": version_result.get('version_number'),
            "created_at": version_result.get('created_at'),
            "material_length": len(key_material),
            "archive_previous": archive_previous,
            "archive_success": archive_success,
            "entropy_report": entropy_report
        }

        print(f"✅ New version created successfully!")
        print(f"   🔑 Key ID: {key_id}")
        print(f"   🔄 Version: {version_result.get('version_number')}")
        print(f"   📅 Created: {version_result.get('created_at')}")

        return create_success_response(response_data, "Key version created successfully")

    except Exception as e:
        print(f"❌ Error creating key version: {str(e)}")
        return create_error_response(f"Failed to create key version: {str(e)}", 500, "version_creation_error")


@ctm_bp.route('/api/v1/ctm/keys/<key_id>/info', methods=['POST'])
def get_key_info(key_id):
    """Get information about a specific key"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        ctm_config = data.get('ctm_config')

        # Validate required parameters
        if not key_id:
            return create_error_response("Key ID is required", 400, "validation_error")

        if not ctm_config:
            return create_error_response("CTM configuration is required", 400, "validation_error")

        # Validate CTM configuration
        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Normalize and create CTM service with dynamic config
        normalized_ctm_config = normalize_ctm_config(ctm_config)
        ctm_service = CTMService(normalized_ctm_config)

        # Get key information
        key_info = ctm_service.get_key_info(key_id)

        if not key_info:
            return create_error_response(f"Key with ID '{key_id}' not found or inaccessible", 404, "key_not_found")

        return create_success_response(key_info, "Key information retrieved successfully")

    except Exception as e:
        return create_error_response(f"Failed to get key information: {str(e)}", 500, "info_retrieval_error")
