# SeQRNG-CTM REST API

Una API REST desarrollada en Flask para integrar el generador de números aleatorios cuánticos SeQRNG con Thales CipherTrust Manager (CTM).

## 🚀 Características

- **Generación de Números Aleatorios Cuánticos**: Genera claves criptográficamente seguras usando tecnología cuántica SeQRNG
- **Integración con CipherTrust Manager**: Sube y gestiona claves en Thales CipherTrust Manager
- **Múltiples Formatos de Claves**: Soporte para generación de claves en bytes, hexadecimal y alfanumérico
- **Operaciones por Lotes**: Sube múltiples claves en una sola operación
- **Monitoreo de Salud**: Estado de la API y configuración en tiempo real
- **Documentación Interactiva**: Interfaz Swagger UI para exploración fácil de la API
- **Configuración Dinámica**: Soporte para configuración dinámica de CTM y SeQRNG

## 🏗️ Arquitectura

```
SeQRNG-CTM API/
├── app/                          # Aplicación principal
│   ├── __init__.py              # Factory pattern de la aplicación
│   ├── controllers/             # Controladores REST
│   │   ├── health_controller.py # Endpoints de salud y documentación
│   │   ├── seqrng_controller.py # Endpoints de generación de claves
│   │   └── ctm_controller.py    # Endpoints de gestión CTM
│   ├── services/                # Lógica de negocio
│   │   ├── seqrng_service.py    # Servicio SeQRNG
│   │   ├── ctm_service.py       # Servicio CTM
│   │   └── validation_service.py # Validaciones
│   ├── config/                  # Configuración
│   │   ├── environment.py       # Variables de entorno
│   │   └── settings.py          # Configuración de la aplicación
│   ├── utils/                   # Utilidades
│   │   ├── response_helpers.py  # Helpers de respuesta
│   │   └── validators.py        # Validadores
│   └── static/                  # Archivos estáticos
│       └── swagger.yaml         # Especificación OpenAPI
├── app.py                       # Punto de entrada simple
├── run.py                       # Punto de entrada principal
├── Dockerfile                   # Configuración Docker
├── requirements.txt             # Dependencias Python
└── .env                        # Variables de entorno
```

## 📋 Requisitos

- Python 3.9+
- Flask 2.3+
- Acceso a SeQRNG API
- Acceso a Thales CipherTrust Manager
- Docker (opcional)

## 🛠️ Instalación

### Instalación Local

1. **Clonar el repositorio**
```bash
git clone <repository-url>
cd sqq-seqrng-api
```

2. **Crear entorno virtual**
```bash
python -m venv venv
source venv/bin/activate  # En Windows: venv\Scripts\activate
```

3. **Instalar dependencias**
```bash
pip install -r requirements.txt
```

4. **Configurar variables de entorno**
```bash
cp .env.example .env
# Editar .env con tus configuraciones
```

### Instalación con Docker

1. **Construir la imagen**
```bash
docker build -t seqrng-ctm-api .
```

2. **Ejecutar el contenedor**
```bash
docker run -p 3001:3001 --env-file .env seqrng-ctm-api
```

## ⚙️ Configuración

### Variables de Entorno (.env)

```bash
# Configuración SeQRNG
SEQRNG_IP_ADDRESS=http://your-seqrng-server:1982
SEQRNG_API_TOKEN=your-seqrng-api-token

# Configuración CTM
CTM_IP_ADDRESS=your-ctm-server
CTM_USERNAME=your-ctm-username
CTM_PASSWORD=your-ctm-password
CTM_DOMAIN=your-ctm-domain

# Configuración Flask
PORT=3001
FLASK_DEBUG=false
FLASK_HOST=0.0.0.0
```

## 🚀 Uso

### Iniciar la Aplicación

```bash
# Desarrollo
python run.py

# Producción
gunicorn -w 4 -b 0.0.0.0:3001 run:app
```

La API estará disponible en: `http://localhost:3001`

### Documentación Interactiva

- **Swagger UI**: `http://localhost:3001/docs`
- **OpenAPI Spec**: `http://localhost:3001/swagger.yaml`
- **Health Check**: `http://localhost:3001/api/v1/health`

## 📚 Endpoints Principales

### Salud y Configuración

- `GET /api/v1/health` - Estado de la API
- `GET /api/v1/config/status` - Estado de configuración
- `GET /api/v1/docs` - Documentación de la API

### Generación de Claves

- `POST /api/v1/keys/generate/bytes` - Generar bytes aleatorios
- `POST /api/v1/keys/generate/hex` - Generar clave hexadecimal
- `POST /api/v1/keys/generate/alphanumeric` - Generar clave alfanumérica
- `POST /api/v1/keys/generate/rsa` - Generar par de claves RSA
- `POST /api/v1/keys/generate/rsa/dynamic` - Generar RSA con configuración dinámica

### Gestión CTM

- `GET /api/v1/ctm/auth/token` - Obtener token de autenticación CTM
- `GET /api/v1/ctm/keys/{key_name}/exists` - Verificar si existe una clave
- `POST /api/v1/ctm/keys/upload` - Subir clave individual
- `POST /api/v1/ctm/keys/upload/batch` - Subir múltiples claves
- `POST /api/v1/ctm/keys/upload/dynamic` - Subir con configuración dinámica
- `POST /api/v1/ctm/keys/upload/batch/dynamic` - Subir lote con configuración dinámica
- `POST /api/v1/ctm/keys/{key_name}/exists/dynamic` - Verificar clave con configuración dinámica
- `POST /api/v1/ctm/auth/token/dynamic` - Obtener token CTM con configuración dinámica

## 💡 Ejemplos de Uso

### Generar Bytes Aleatorios

```bash
curl -X POST http://localhost:3001/api/v1/keys/generate/bytes \
  -H "Content-Type: application/json" \
  -d '{"num_bytes": 32, "packages": 1}'
```

### Subir Clave a CTM

```bash
curl -X POST http://localhost:3001/api/v1/ctm/keys/upload \
  -H "Content-Type: application/json" \
  -d '{
    "key_name": "my_quantum_key",
    "algorithm": "AES",
    "num_bytes": 32,
    "owner": "api_user",
    "exportable": false
  }'
```

### Subir Múltiples Claves

```bash
curl -X POST http://localhost:3001/api/v1/ctm/keys/upload/batch \
  -H "Content-Type: application/json" \
  -d '{
    "key_base_name": "quantum_key",
    "algorithm": "AES",
    "num_bytes": 32,
    "key_count": 5,
    "owner": "api_user",
    "exportable": false
  }'
```

### Subir Clave con Configuración Dinámica

```bash
curl -X POST http://localhost:3001/api/v1/ctm/keys/upload/dynamic \
  -H "Content-Type: application/json" \
  -d '{
    "key_name": "dynamic_quantum_key",
    "algorithm": "AES",
    "num_bytes": 32,
    "owner": "api_user",
    "exportable": false,
    "ctm_config": {
      "ip_address": "*************",
      "username": "admin",
      "password": "password123",
      "domain": "root"
    },
    "seqrng_config": {
      "ip_address": "http://*************:1982",
      "api_token": "1|abcdef123456789"
    }
  }'
```

## 🔒 Seguridad

- Todo el material de claves se genera usando generadores de números aleatorios cuánticos
- Las claves se transmiten de forma segura usando codificación Base64
- Integración con Thales CipherTrust Manager de nivel empresarial
- Configuración de propiedad y exportabilidad de claves
- Autenticación requerida para operaciones CTM

## 🧪 Algoritmos Soportados

- **AES**: 16, 24, o 32 bytes (128, 192, 256 bits)
- **RSA**: Material de clave de longitud variable (1024, 2048, 3072, 4096 bits)
- **HMAC**: Material de clave de longitud variable

## 📊 Monitoreo

### Health Check

```bash
curl http://localhost:3001/api/v1/health
```

Respuesta:
```json
{
  "status": "success",
  "message": "Health check successful",
  "data": {
    "service": "SeQRNG-CTM REST API",
    "version": "1.0.0",
    "status": "healthy",
    "configuration_loaded": true,
    "endpoints": {
      "key_generation": "/api/v1/keys/generate/*",
      "ctm_management": "/api/v1/ctm/*",
      "health": "/api/v1/health",
      "config": "/api/v1/config/status",
      "docs": "/api/v1/docs"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🐛 Solución de Problemas

### Problemas Comunes

1. **Error de configuración**: Verificar variables de entorno en `.env`
2. **Conexión SeQRNG**: Verificar URL y token de API
3. **Conexión CTM**: Verificar credenciales y conectividad de red
4. **Puerto en uso**: Cambiar puerto en variable `PORT` del `.env`

### Logs

Los logs se muestran en la consola durante el desarrollo. Para producción, configurar logging apropiado.

## 📄 Licencia

Por definir

## 🔄 Versiones

- **v1.0.0**: Versión inicial con funcionalidad completa de SeQRNG-CTM
