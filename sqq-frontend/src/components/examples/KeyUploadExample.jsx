/**
 * Ejemplo de componente para subir llaves usando los servicios
 */

import React, { useState } from 'react';
import { Upload, Key, AlertCircle, CheckCircle } from 'lucide-react';
import { useKeys, useAuth } from '../../hooks/index';

const KeyUploadExample = () => {
  const { user } = useAuth();
  const { uploadToCTM, isLoading, error, clearError } = useKeys();
  
  const [keyData, setKeyData] = useState({
    key_name: '',
    algorithm: 'AES',
    num_bytes: 32,
    exportable: false,
  });
  
  const [success, setSuccess] = useState(false);
  const [result, setResult] = useState(null);

  const handleInputChange = (field, value) => {
    setKeyData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Limpiar errores al cambiar datos
    if (error) clearError();
    if (success) setSuccess(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      const response = await uploadToCTM(keyData);
      setResult(response);
      setSuccess(true);
      
      // Limpiar formulario
      setKeyData({
        key_name: '',
        algorithm: 'AES',
        num_bytes: 32,
        exportable: false,
      });
    } catch (error) {
      console.error('Error uploading key:', error);
    }
  };

  const generateRandomKey = () => {
    const bytes = new Uint8Array(keyData.num_bytes);
    crypto.getRandomValues(bytes);
    const base64 = btoa(String.fromCharCode(...bytes));
    handleInputChange('key_material_base64', base64);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center gap-3 mb-6">
        <Key className="text-blue-500" size={24} />
        <h2 className="text-2xl font-bold text-gray-900">Subir Llave a CTM</h2>
      </div>

      {/* Mostrar información del usuario */}
      {user && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <p className="text-sm text-blue-700">
            <strong>Usuario:</strong> {user.firstName} {user.lastName} ({user.email})
          </p>
          {user.company && (
            <p className="text-sm text-blue-700">
              <strong>Empresa:</strong> {user.company}
            </p>
          )}
        </div>
      )}

      {/* Mostrar errores */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3">
          <AlertCircle className="text-red-500" size={20} />
          <div>
            <p className="text-red-700 font-medium">Error al subir la llave</p>
            <p className="text-red-600 text-sm">{error}</p>
          </div>
          <button
            onClick={clearError}
            className="ml-auto text-red-500 hover:text-red-700"
          >
            ✕
          </button>
        </div>
      )}

      {/* Mostrar éxito */}
      {success && result && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-3 mb-2">
            <CheckCircle className="text-green-500" size={20} />
            <p className="text-green-700 font-medium">¡Llave subida exitosamente!</p>
          </div>
          <div className="text-sm text-green-600">
            <p><strong>ID:</strong> {result.id}</p>
            <p><strong>Nombre:</strong> {result.name}</p>
            <p><strong>Estado:</strong> {result.status}</p>
            <p><strong>Algoritmo:</strong> {result.algorithm}</p>
            <p><strong>Bytes:</strong> {result.numBytes}</p>
            {result.entropyReport && (
              <p><strong>Entropía:</strong> Verificada</p>
            )}
          </div>
        </div>
      )}

      {/* Formulario */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Nombre de la llave */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nombre de la Llave *
          </label>
          <input
            type="text"
            value={keyData.key_name}
            onChange={(e) => handleInputChange('key_name', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="mi-llave-encriptacion-001"
            required
            disabled={isLoading}
          />
          <p className="text-xs text-gray-500 mt-1">
            Solo letras, números, guiones y guiones bajos
          </p>
        </div>

        {/* Algoritmo */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Algoritmo
          </label>
          <select
            value={keyData.algorithm}
            onChange={(e) => handleInputChange('algorithm', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isLoading}
          >
            <option value="AES">AES</option>
            <option value="RSA">RSA</option>
            <option value="HMAC">HMAC</option>
          </select>
        </div>

        {/* Número de bytes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Número de Bytes
          </label>
          <input
            type="number"
            value={keyData.num_bytes}
            onChange={(e) => handleInputChange('num_bytes', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            min="1"
            max="1024"
            disabled={isLoading}
          />
          <p className="text-xs text-gray-500 mt-1">
            Entre 1 y 1024 bytes
          </p>
        </div>

        {/* Exportable */}
        <div className="flex items-center gap-3">
          <input
            type="checkbox"
            id="exportable"
            checked={keyData.exportable}
            onChange={(e) => handleInputChange('exportable', e.target.checked)}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            disabled={isLoading}
          />
          <label htmlFor="exportable" className="text-sm font-medium text-gray-700">
            Llave exportable
          </label>
        </div>

        {/* Material de la llave */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Material de la Llave (Base64)
          </label>
          <div className="flex gap-2">
            <textarea
              value={keyData.key_material_base64}
              onChange={(e) => handleInputChange('key_material_base64', e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
              placeholder="Opcional - se generará automáticamente si se deja vacío"
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={generateRandomKey}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition"
              disabled={isLoading}
            >
              Generar
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Si se deja vacío, se generará automáticamente
          </p>
        </div>

        {/* Botón de envío */}
        <button
          type="submit"
          disabled={isLoading || !keyData.key_name}
          className={`w-full py-3 px-4 rounded-lg font-medium transition flex items-center justify-center gap-2 ${
            isLoading || !keyData.key_name
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isLoading ? (
            <>
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Subiendo llave...
            </>
          ) : (
            <>
              <Upload size={20} />
              Subir Llave a CTM
            </>
          )}
        </button>
      </form>
    </div>
  );
};

export default KeyUploadExample;
