import { Trash2, <PERSON>, <PERSON>fresh<PERSON><PERSON> } from 'lucide-react';
import PropTypes from 'prop-types';
import { Button } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Componente para mostrar información de una llave en formato de tarjeta
 */
const KeyCard = ({ keyData, darkMode, onDelete, onViewDetail, onCreateVersion, disabled }) => {
  const { t, currentLanguage } = useLanguage();
  const getStatusInfo = (key) => {
    if (key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true) {
      return {
        text: 'uploaded_to_ctm',
        displayText: t('keys.status.active'),
        className: 'bg-green-500 text-white',
        dotColor: 'bg-green-400'
      };
    }
    if (key.status === 'FAILED' || key.isSuccessful === false) {
      return {
        text: 'failed',
        displayText: t('keys.status.failed'),
        className: 'bg-red-500 text-white',
        dotColor: 'bg-red-400'
      };
    }
    return {
      text: 'pending',
      displayText: t('keys.status.pending'),
      className: 'bg-yellow-500 text-white',
      dotColor: 'bg-yellow-400'
    };
  };

  const getTypeInfo = (key) => {
    // Priorizar algorithm sobre type, ya que type puede contener 'hex_key'
    const type = key.algorithm || key.type || 'unknown';
    return {
      text: type,
      className: 'bg-blue-500 text-white'
    };
  };

  const statusInfo = getStatusInfo(keyData);
  const typeInfo = getTypeInfo(keyData);

  return (
    <div
      className={`p-4 sm:p-6 rounded-2xl border-2 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 ${
        darkMode
          ? 'bg-gradient-to-br from-gray-800 to-gray-700 border-gray-600 hover:border-gray-500'
          : 'bg-gradient-to-br from-white to-gray-50 border-gray-200 hover:border-gray-300'
      }`}
    >
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
        <div className="flex-1">
          {/* Header con nombre y etiquetas */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-3 mb-4">
            <h3 className="text-lg sm:text-xl font-light tracking-wide text-gray-900 dark:text-white">
              {keyData.name}
            </h3>

            <div className="flex flex-wrap gap-2">
              {/* Etiqueta de estado moderna */}
              <div className={`flex items-center gap-2 px-3 py-1.5 rounded-xl text-xs font-medium ${statusInfo.className}`}>
                <div className={`w-2 h-2 rounded-full ${statusInfo.dotColor} animate-pulse`}></div>
                {statusInfo.displayText}
              </div>

              {/* Etiqueta de tipo moderna */}
              <div className={`px-3 py-1.5 rounded-xl text-xs font-medium ${typeInfo.className}`}>
                {typeInfo.text}
              </div>
            </div>
          </div>

          {/* ID de la llave con diseño elegante */}
          <div className="mb-4">
            <div className={`p-3 sm:p-4 rounded-xl border-2 ${
              darkMode
                ? 'bg-gray-900/50 border-gray-600'
                : 'bg-gray-50 border-gray-200'
            }`}>
              <p className="font-mono text-xs sm:text-sm text-gray-700 dark:text-gray-300 break-all">
                {keyData.ctmKeyId || keyData.id}
              </p>
              <p className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 mt-1">
                {keyData.ctmKeyId ? t('keys.details.ctmKeyId') : t('keys.details.localId')}
              </p>
            </div>
          </div>

          {/* Información en grid elegante */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
            <div className={`p-3 rounded-xl ${
              darkMode ? 'bg-gray-900/30' : 'bg-white/70'
            }`}>
              <span className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400">{t('keys.details.created')}:</span>
              <p className="font-light tracking-wide text-gray-900 dark:text-white text-sm sm:text-base">
                {keyData.createdAt ? new Date(keyData.createdAt).toLocaleDateString(
                  currentLanguage === 'es' ? 'es-ES' : 'en-US'
                ) : 'N/A'}
              </p>
            </div>
            <div className={`p-3 rounded-xl ${
              darkMode ? 'bg-gray-900/30' : 'bg-white/70'
            }`}>
              <span className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400">{t('keys.details.algorithm')}:</span>
              <p className="font-light tracking-wide text-gray-900 dark:text-white text-sm sm:text-base">
                {keyData.algorithm || keyData.type || 'N/A'}
              </p>
            </div>
          </div>
        </div>

        {/* Botones de acción limpios */}
        <div className="flex flex-row gap-3 lg:ml-6 justify-center lg:justify-start">
          <button
            onClick={() => onViewDetail(keyData)}
            disabled={disabled}
            className={`p-2 border rounded-lg transition-all duration-200 transform ${
              disabled
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-gray-50 text-blue-500 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-blue-400 dark:hover:bg-blue-900/20 dark:hover:border-blue-500'
            }`}
            title="Ver detalle de la llave"
          >
            <Eye size={18} />
          </button>
          {/* Solo mostrar botón de crear versión si la llave está subida a CTM */}
          {(keyData.status === 'UPLOADED_TO_CTM' || keyData.uploadedToCtm === true) && onCreateVersion && (
            <button
              onClick={() => onCreateVersion(keyData)}
              disabled={disabled}
              className={`p-2 border rounded-lg transition-all duration-200 transform ${
                disabled
                  ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                  : 'border-gray-200 bg-gray-50 text-green-500 hover:bg-green-50 hover:border-green-300 hover:text-green-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-green-400 dark:hover:bg-green-900/20 dark:hover:border-green-500'
              }`}
              title="Crear nueva versión"
            >
              <RefreshCw size={18} />
            </button>
          )}
          <button
            onClick={onDelete}
            disabled={disabled}
            className={`p-2 border rounded-lg transition-all duration-200 transform ${
              disabled
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-gray-50 text-red-500 hover:bg-red-50 hover:border-red-300 hover:text-red-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:hover:border-red-500'
            }`}
            title="Eliminar llave"
          >
            <Trash2 size={18} />
          </button>
        </div>
      </div>
    </div>
  );
};

KeyCard.propTypes = {
  keyData: PropTypes.object.isRequired,
  darkMode: PropTypes.bool.isRequired,
  onDelete: PropTypes.func.isRequired,
  onViewDetail: PropTypes.func.isRequired,
  onCreateVersion: PropTypes.func,
  disabled: PropTypes.bool
};

export default KeyCard;
