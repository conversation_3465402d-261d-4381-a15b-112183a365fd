import { useState } from 'react';
import { X, RefreshCw, Archive, AlertTriangle } from 'lucide-react';
import PropTypes from 'prop-types';
import { But<PERSON>, LoadingSpinner } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Modal para crear nueva versión de una llave existente
 */
const KeyVersionModal = ({ isOpen, onClose, onCreateVersion, keyData, darkMode }) => {
  const [archivePrevious, setArchivePrevious] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { t } = useLanguage();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      await onCreateVersion({
        keyId: keyData.ctmKeyId || keyData.id,
        archivePrevious
      });
      onClose();
      setArchivePrevious(false);
    } catch (err) {
      setError(err.message || 'Error al crear nueva versión de la llave');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setError('');
      setArchivePrevious(false);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className={`w-full max-w-md rounded-2xl shadow-2xl ${
        darkMode ? 'bg-gray-800' : 'bg-white'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-light tracking-wide text-gray-900 dark:text-white">
            Crear Nueva Versión
          </h2>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X size={20} className="text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6">
          {/* Key Information */}
          <div className={`p-4 rounded-xl mb-6 ${
            darkMode ? 'bg-gray-900/50' : 'bg-gray-50'
          }`}>
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              Llave Seleccionada
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-1">
              <span className="font-medium">Nombre:</span> {keyData?.name}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-1">
              <span className="font-medium">Algoritmo:</span> {keyData?.algorithm || keyData?.type}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-300 font-mono break-all">
              <span className="font-medium">ID:</span> {keyData?.ctmKeyId || keyData?.id}
            </p>
          </div>

          {/* Warning */}
          <div className={`p-4 rounded-xl mb-6 border-l-4 border-yellow-500 ${
            darkMode ? 'bg-yellow-900/20' : 'bg-yellow-50'
          }`}>
            <div className="flex items-start gap-3">
              <AlertTriangle size={20} className="text-yellow-500 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                  Importante
                </h4>
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  Se creará una nueva versión de esta llave usando material cuántico fresco de SeQRNG. 
                  La versión anterior permanecerá disponible a menos que elijas archivarla.
                </p>
              </div>
            </div>
          </div>

          {/* Archive Previous Option */}
          <div className="mb-6">
            <label className="flex items-start gap-3 cursor-pointer">
              <input
                type="checkbox"
                checked={archivePrevious}
                onChange={(e) => setArchivePrevious(e.target.checked)}
                disabled={isLoading}
                className="mt-1 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              />
              <div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Archivar versión anterior
                </span>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  La versión anterior se marcará como archivada en CTM después de crear la nueva versión
                </p>
              </div>
            </label>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-3">
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
              disabled={isLoading}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 flex items-center justify-center gap-2"
            >
              {isLoading ? (
                <>
                  <LoadingSpinner size="sm" />
                  Creando...
                </>
              ) : (
                <>
                  <RefreshCw size={16} />
                  Crear Versión
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

KeyVersionModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onCreateVersion: PropTypes.func.isRequired,
  keyData: PropTypes.object,
  darkMode: PropTypes.bool.isRequired
};

export default KeyVersionModal;
