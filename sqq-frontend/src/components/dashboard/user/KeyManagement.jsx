import { useState } from 'react';
import { Plus, Trash2, Chevron<PERSON>ef<PERSON>, ChevronRight } from 'lucide-react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, Loading<PERSON>pinner, <PERSON>rror<PERSON><PERSON><PERSON> } from '../../common';
import { useLanguage } from '../../../hooks';
import KeyCard from './KeyCard';
import KeyUploadModal from './KeyUploadModal';
import KeyDeleteModal from './KeyDeleteModal';
import KeyVersionModal from './KeyVersionModal';
import { KeyDetailModal } from '../admin'; // Reutilizamos el modal del admin

/**
 * Componente para gestión de llaves en el UsuarioDashboard
 */
const KeyManagement = ({
  keys,
  isLoading,
  error,
  onClearError,
  onUploadKey,
  onDeleteKey,
  onCreateKeyVersion,
  darkMode
}) => {
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showVersionModal, setShowVersionModal] = useState(false);
  const [selectedKey, setSelectedKey] = useState(null);
  const [keyToDelete, setKeyToDelete] = useState(null);
  const [keyForVersion, setKeyForVersion] = useState(null);

  // Estados para paginación
  const [currentPage, setCurrentPage] = useState(1);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationDirection, setAnimationDirection] = useState('');

  const { t } = useLanguage();

  // Configuración de paginación
  const KEYS_PER_PAGE = 3;
  const totalPages = Math.ceil(keys.length / KEYS_PER_PAGE);
  const startIndex = (currentPage - 1) * KEYS_PER_PAGE;
  const endIndex = startIndex + KEYS_PER_PAGE;
  const currentKeys = keys.slice(startIndex, endIndex);
  const totalKeys = keys.length;

  const handleUploadKey = async (keyData) => {
    await onUploadKey(keyData);
    setShowUploadModal(false);
  };

  // Funciones de paginación con animación
  const handlePageChange = (newPage, direction) => {
    if (isAnimating || newPage === currentPage || newPage < 1 || newPage > totalPages) return;

    setIsAnimating(true);
    setAnimationDirection(direction);

    // Cambiar página inmediatamente y luego quitar animación
    setCurrentPage(newPage);

    setTimeout(() => {
      setIsAnimating(false);
      setAnimationDirection('');
    }, 300);
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1, 'next');
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1, 'prev');
    }
  };

  const handleDeleteKey = (keyId, keyName) => {
    setKeyToDelete({ id: keyId, name: keyName });
    setShowDeleteModal(true);
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setKeyToDelete(null);
  };

  const handleViewDetail = (key) => {
    setSelectedKey(key);
    setShowDetailModal(true);
  };

  const handleCloseDetailModal = () => {
    setShowDetailModal(false);
    setSelectedKey(null);
  };

  const handleCreateVersion = (key) => {
    setKeyForVersion(key);
    setShowVersionModal(true);
  };

  const handleCreateKeyVersion = async (versionData) => {
    await onCreateKeyVersion(versionData);
    setShowVersionModal(false);
    setKeyForVersion(null);
  };

  const handleCloseVersionModal = () => {
    setShowVersionModal(false);
    setKeyForVersion(null);
  };

  // Ajustar página después de eliminar
  const handleConfirmDeleteWithPagination = async () => {
    try {
      await onDeleteKey(keyToDelete.id);
      setShowDeleteModal(false);
      setKeyToDelete(null);

      // Ajustar página si es necesario después de eliminar
      const newTotalPages = Math.ceil((keys.length - 1) / KEYS_PER_PAGE);
      if (currentPage > newTotalPages && newTotalPages > 0) {
        setCurrentPage(newTotalPages);
      }
    } catch (error) {
      console.error('Error deleting key:', error);
    }
  };

  // Componente de paginación estilo Gmail
  const PaginationControls = () => {
    if (totalKeys === 0) return null;

    const startItem = startIndex + 1;
    const endItem = Math.min(endIndex, totalKeys);

    return (
      <div className={`flex items-center justify-between px-3 py-2 rounded-lg border ${
        darkMode ? 'bg-gray-800/50 border-gray-700' : 'bg-gray-50 border-gray-200'
      }`}>
        <div className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400">
          {startItem}-{endItem} de {totalKeys} {totalKeys === 1 ? 'llave' : 'llaves'}
        </div>

        <div className="flex items-center gap-1">
          <button
            onClick={goToPrevPage}
            disabled={currentPage === 1 || isAnimating}
            className={`p-1.5 rounded border transition-all duration-200 ${
              currentPage === 1 || isAnimating
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-100 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
            }`}
            title="Página anterior"
          >
            <ChevronLeft size={14} />
          </button>

          <button
            onClick={goToNextPage}
            disabled={currentPage === totalPages || isAnimating}
            className={`p-1.5 rounded border transition-all duration-200 ${
              currentPage === totalPages || isAnimating
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-100 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
            }`}
            title="Página siguiente"
          >
            <ChevronRight size={14} />
          </button>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Header con botón en la esquina superior derecha */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-6">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl font-light tracking-wide leading-relaxed mb-2 text-gray-900 dark:text-white">
              {t('keys.title')}
            </h1>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 font-light tracking-wide">
              {t('keys.subtitle')}
            </p>
          </div>

          <div className="flex justify-center sm:justify-end">
            <button
              onClick={() => setShowUploadModal(true)}
              className="flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm sm:text-base"
            >
              <Plus size={16} />
              <span>{t('keys.uploadNew')}</span>
            </button>
          </div>
        </div>
      </div>

      <ErrorAlert error={error} onClose={onClearError} />

      {/* Controles de paginación estilo Gmail - ARRIBA */}
      {!isLoading && keys.length > 0 && totalPages > 1 && (
        <div className="mb-6">
          <PaginationControls />
        </div>
      )}

      {isLoading ? (
        <LoadingSpinner message="Cargando llaves..." />
      ) : (
        <>
          {keys.length === 0 ? (
            <div className="text-center py-12 sm:py-16">
              <div className="max-w-md mx-auto">
                <div className="text-gray-400 mb-4">
                  <Plus size={48} className="mx-auto" />
                </div>
                <p className="text-gray-500 dark:text-gray-400 text-sm sm:text-base">
                  {t('keys.noKeys')}
                </p>
                <button
                  onClick={() => setShowUploadModal(true)}
                  className="mt-4 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium text-sm"
                >
                  {t('keys.uploadNew')}
                </button>
              </div>
            </div>
          ) : (
            <>
              {/* Contenedor con animaciones para las llaves */}
              <div
                className={`space-y-3 sm:space-y-4 transition-all duration-300 ease-in-out ${
                  isAnimating
                    ? animationDirection === 'next'
                      ? 'transform translate-x-4 opacity-70'
                      : 'transform -translate-x-4 opacity-70'
                    : 'transform translate-x-0 opacity-100'
                }`}
              >
                {currentKeys.map((key) => (
                  <KeyCard
                    key={key.id}
                    keyData={key}
                    darkMode={darkMode}
                    onDelete={() => handleDeleteKey(key.id, key.name)}
                    onViewDetail={handleViewDetail}
                    onCreateVersion={handleCreateVersion}
                    disabled={isLoading || isAnimating}
                  />
                ))}
              </div>


            </>
          )}
        </>
      )}



      <KeyUploadModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUpload={handleUploadKey}
        darkMode={darkMode}
      />

      <KeyDetailModal
        isOpen={showDetailModal}
        onClose={handleCloseDetailModal}
        keyData={selectedKey}
        darkMode={darkMode}
      />

      <KeyDeleteModal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDeleteWithPagination}
        keyName={keyToDelete?.name || ''}
        darkMode={darkMode}
      />

      <KeyVersionModal
        isOpen={showVersionModal}
        onClose={handleCloseVersionModal}
        onCreateVersion={handleCreateKeyVersion}
        keyData={keyForVersion}
        darkMode={darkMode}
      />
    </>
  );
};

KeyManagement.propTypes = {
  keys: PropTypes.array.isRequired,
  isLoading: PropTypes.bool.isRequired,
  error: PropTypes.string,
  onClearError: PropTypes.func.isRequired,
  onUploadKey: PropTypes.func.isRequired,
  onDeleteKey: PropTypes.func.isRequired,
  onCreateKeyVersion: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default KeyManagement;
