import { Trash2, AlertTriangle } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Modal elegante para confirmar eliminación de llave
 */
const KeyDeleteModal = ({ isOpen, onClose, onConfirm, keyName, darkMode }) => {
  const { t } = useLanguage();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-3">
          <AlertTriangle size={24} className="text-red-500" />
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('keys.delete.title')}
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('keys.delete.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-lg"
      darkMode={darkMode}
    >
      {/* Contenido del modal */}
      <div className="space-y-4">
        {/* Mensaje de advertencia */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode
            ? 'bg-red-900/10 border-red-700'
            : 'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-start gap-3">
            <Trash2 size={18} className="text-red-500 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-base font-light tracking-wide text-red-700 dark:text-red-300 mb-3">
                {t('keys.delete.confirmQuestion')}
              </h4>
              <div className={`p-3 rounded-lg border mb-3 ${
                darkMode
                  ? 'bg-gray-800 border-gray-700'
                  : 'bg-white border-gray-200'
              }`}>
                <p className="font-mono text-sm font-light tracking-wide text-gray-900 dark:text-white">
                  "{keyName}"
                </p>
              </div>
              <p className="text-sm font-light tracking-wide text-red-600 dark:text-red-400">
                {t('keys.delete.warningMessage')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer con botones */}
      <div className="flex justify-center gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={onConfirm}
          className="px-8 py-3 rounded-xl bg-red-600 hover:bg-red-700 text-white font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-2"
        >
          <Trash2 size={16} />
          {t('keys.delete.deleteButton')}
        </button>

        <button
          onClick={onClose}
          className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300"
        >
          {t('common.cancel')}
        </button>
      </div>
    </Modal>
  );
};

KeyDeleteModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  keyName: PropTypes.string.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default KeyDeleteModal;
