import { useState, useEffect } from 'react';
import { Save } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal, Button, FormField } from '../../common';
import { useLanguage } from '../../../hooks/useLanguage';

/**
 * Modal para editar información de usuario
 */
const UserEditModal = ({ isOpen, user, onClose, onSave, darkMode }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    role: 'USER',
    isActive: true,
    ctmIpAddress: '',
    ctmUsername: '',
    ctmPassword: '',
    ctmDomain: '',
    seqrngIpAddress: '',
    seqrngApiToken: ''
  });
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        company: user.company || '',
        role: user.role || 'USER',
        isActive: user.isActive !== undefined ? user.isActive : true,
        ctmIpAddress: user.ctmIpAddress || '',
        ctmUsername: user.ctmUsername || '',
        ctmPassword: user.ctmPassword || '',
        ctmDomain: user.ctmDomain || '',
        seqrngIpAddress: user.seqrngIpAddress || '',
        seqrngApiToken: user.seqrngApiToken || ''
      });
    }
  }, [user]);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);
    try {
      await onSave(formData);
    } finally {
      setIsSaving(false);
    }
  };

  const roleOptions = [
    { value: 'USER', label: t('admin.users.edit.roles.user') },
    { value: 'ADMIN', label: t('admin.users.edit.roles.admin') }
  ];

  const statusOptions = [
    { value: true, label: t('admin.users.edit.statusOptions.active') },
    { value: false, label: t('admin.users.edit.statusOptions.inactive') }
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t('admin.users.edit.title')}
      maxWidth="max-w-4xl"
      darkMode={darkMode}
      className="max-h-[90vh] overflow-y-auto"
    >
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Información Personal */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold border-b pb-2">{t('admin.users.edit.personalInfo')}</h4>

            <FormField
              label={t('admin.users.edit.firstName')}
              type="text"
              value={formData.firstName}
              onChange={(e) => handleChange('firstName', e.target.value)}
              required
              darkMode={darkMode}
            />

            <FormField
              label={t('admin.users.edit.lastName')}
              type="text"
              value={formData.lastName}
              onChange={(e) => handleChange('lastName', e.target.value)}
              required
              darkMode={darkMode}
            />

            <FormField
              label={t('admin.users.edit.email')}
              type="email"
              value={formData.email}
              onChange={(e) => handleChange('email', e.target.value)}
              required
              darkMode={darkMode}
            />

            <FormField
              label={t('admin.users.edit.company')}
              type="text"
              value={formData.company}
              onChange={(e) => handleChange('company', e.target.value)}
              darkMode={darkMode}
            />

            <FormField
              label={t('admin.users.edit.role')}
              type="select"
              value={formData.role}
              onChange={(e) => handleChange('role', e.target.value)}
              options={roleOptions}
              darkMode={darkMode}
            />

            <FormField
              label={t('admin.users.edit.status')}
              type="select"
              value={formData.isActive}
              onChange={(e) => handleChange('isActive', e.target.value === 'true')}
              options={statusOptions}
              darkMode={darkMode}
            />
          </div>

          {/* Configuración de Servicios */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold border-b pb-2">{t('admin.users.edit.serviceConfig')}</h4>

            {/* CTM Configuration */}
            <div className="space-y-3">
              <h5 className="font-medium text-blue-600 dark:text-blue-400">{t('admin.users.edit.ctm.title')}</h5>

              <FormField
                label={t('admin.users.edit.ctm.ipAddress')}
                type="text"
                value={formData.ctmIpAddress}
                onChange={(e) => handleChange('ctmIpAddress', e.target.value)}
                placeholder="https://ctm.example.com:443"
                darkMode={darkMode}
              />

              <FormField
                label={t('admin.users.edit.ctm.username')}
                type="text"
                value={formData.ctmUsername}
                onChange={(e) => handleChange('ctmUsername', e.target.value)}
                darkMode={darkMode}
              />

              <FormField
                label={t('admin.users.edit.ctm.password')}
                type="password"
                value={formData.ctmPassword}
                onChange={(e) => handleChange('ctmPassword', e.target.value)}
                darkMode={darkMode}
              />

              <FormField
                label={t('admin.users.edit.ctm.domain')}
                type="text"
                value={formData.ctmDomain}
                onChange={(e) => handleChange('ctmDomain', e.target.value)}
                placeholder="root"
                darkMode={darkMode}
              />
            </div>

            {/* SEQRNG Configuration */}
            <div className="space-y-3">
              <h5 className="font-medium text-green-600 dark:text-green-400">{t('admin.users.edit.seqrng.title')}</h5>

              <FormField
                label={t('admin.users.edit.seqrng.ipAddress')}
                type="text"
                value={formData.seqrngIpAddress}
                onChange={(e) => handleChange('seqrngIpAddress', e.target.value)}
                placeholder="https://seqrng.example.com:1982"
                darkMode={darkMode}
              />

              <FormField
                label={t('admin.users.edit.seqrng.apiToken')}
                type="text"
                value={formData.seqrngApiToken}
                onChange={(e) => handleChange('seqrngApiToken', e.target.value)}
                placeholder="1|cifovFzUWTeAPUeAYlZYefsY3tXFf9Z1TV5WV0YL"
                darkMode={darkMode}
              />
            </div>
          </div>
        </div>

        {/* Footer fijo centrado */}
        <div className="flex justify-center gap-4 mt-6 pt-6 border-t-2 border-gray-200 dark:border-gray-600 bg-gray-50/30 dark:bg-gray-700/20 px-8 pb-6 flex-shrink-0 rounded-b-2xl -mx-8">
          <button
            type="submit"
            disabled={isSaving}
            className="px-8 py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-3"
          >
            <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
              <Save size={16} className={isSaving ? "animate-spin" : ""} />
            </div>
            <span>
              {isSaving ? t('admin.users.edit.saving') : t('admin.users.edit.saveButton')}
            </span>
          </button>

          <button
            type="button"
            onClick={onClose}
            disabled={isSaving}
            className="px-8 py-3 rounded-xl font-light tracking-wide border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 transform hover:scale-105 transition-all duration-300 disabled:opacity-50"
          >
            {t('common.cancel')}
          </button>
        </div>
      </form>
    </Modal>
  );
};

UserEditModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  user: PropTypes.object,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default UserEditModal;
