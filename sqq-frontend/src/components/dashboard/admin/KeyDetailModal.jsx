import { Copy, Key, Calendar, Shield, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal, Button } from '../../common';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { useLanguage } from '../../../hooks';

/**
 * Modal para mostrar el detalle completo de una llave
 */
const KeyDetailModal = ({
  isOpen,
  onClose,
  keyData,
  darkMode,
  isAdmin = false
}) => {
  const rgbCardRef = useRef(null);
  const { t, currentLanguage } = useLanguage();

  // Función para obtener las traducciones correctas según el contexto
  const getTranslation = (key) => {
    if (isAdmin) {
      return t(`admin.users.keys.detail.${key}`);
    }
    return t(`keys.detail.${key}`);
  };

  // Efecto RGB fluido diagonal (superior izquierda → inferior derecha)
  useEffect(() => {
    if (isOpen && rgbCardRef.current) {
      const tl = gsap.timeline({ repeat: -1 });

      // Flujo diagonal continuo desde esquina superior izquierda a inferior derecha
      tl.to(rgbCardRef.current, {
        backgroundPosition: "100% 100%",
        duration: 12,
        ease: "power2.inOut",
      })
      .to(rgbCardRef.current, {
        backgroundPosition: "0% 0%",
        duration: 12,
        ease: "power2.inOut",
      });

      return () => tl.kill();
    }
  }, [isOpen]);
  if (!keyData) return null;

  const copyToClipboard = (text, label) => {
    navigator.clipboard.writeText(text).then(() => {
      // Aquí podrías agregar una notificación de éxito
      console.log(`${label} ${t('keys.detail.copyTooltip')}`);
    }).catch(() => {
      console.error('Error al copiar al portapapeles');
    });
  };

  // Función para parsear el entropy report
  const parseEntropyReport = (entropyReportString) => {
    try {
      if (!entropyReportString) return null;
      return JSON.parse(entropyReportString);
    } catch (error) {
      console.error('Error parsing entropy report:', error);
      return null;
    }
  };

  const getStatusInfo = () => {
    if (keyData.status === 'UPLOADED_TO_CTM' || keyData.uploadedToCtm === true) {
      return {
        icon: <CheckCircle size={24} className="text-green-500" />,
        text: isAdmin ? t('admin.users.keys.statusActive') : t('keys.status.active'),
        color: 'text-green-700 dark:text-green-300',
        bgColor: 'bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30',
        borderColor: 'border-green-200 dark:border-green-700'
      };
    }
    if (keyData.status === 'FAILED' || keyData.isSuccessful === false) {
      return {
        icon: <AlertCircle size={24} className="text-red-500" />,
        text: isAdmin ? t('admin.users.keys.statusFailed') : t('keys.status.failed'),
        color: 'text-red-700 dark:text-red-300',
        bgColor: 'bg-gradient-to-br from-red-50 to-rose-100 dark:from-red-900/30 dark:to-rose-900/30',
        borderColor: 'border-red-200 dark:border-red-700'
      };
    }
    return {
      icon: <Clock size={24} className="text-yellow-500" />,
      text: isAdmin ? t('admin.users.keys.statusPending') : t('keys.status.pending'),
      color: 'text-yellow-700 dark:text-yellow-300',
      bgColor: 'bg-gradient-to-br from-yellow-50 to-amber-100 dark:from-yellow-900/30 dark:to-amber-900/30',
      borderColor: 'border-yellow-200 dark:border-yellow-700'
    };
  };

  const statusInfo = getStatusInfo();

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString(
      currentLanguage === 'es' ? 'es-ES' : 'en-US'
    );
  };

  const DetailRow = ({ label, value, copyable = false }) => (
    <div className="flex justify-between items-center py-3 px-4 hover:bg-white/20 dark:hover:bg-gray-700/30 rounded-lg transition-colors duration-200">
      <span className="text-sm font-light tracking-wide text-gray-700 dark:text-gray-300">
        {label}
      </span>
      <div className="flex items-center gap-2">
        <span className="text-sm font-light tracking-wide text-gray-900 dark:text-gray-100">
          {value || 'N/A'}
        </span>
        {copyable && value && (
          <button
            onClick={() => copyToClipboard(value, label)}
            className="p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200"
            title={`${t('keys.detail.copyTooltip')} ${label}`}
          >
            <Copy size={14} />
          </button>
        )}
      </div>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-3">
          <Key size={24} className="text-blue-500" />
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {getTranslation('title')}
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {keyData.name}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-2xl"
      darkMode={darkMode}
    >
      <div className="space-y-4">
        {/* Estado */}
        <div className={`p-4 rounded-xl border shadow-sm ${statusInfo.bgColor} ${statusInfo.borderColor}`}>
          <div className="flex items-center gap-3">
            {statusInfo.icon}
            <div>
              <h4 className={`text-base font-light tracking-wide ${statusInfo.color} mb-1`}>
                {t('keys.detail.status')}: {statusInfo.text}
              </h4>
              <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                {keyData.status === 'UPLOADED_TO_CTM'
                  ? t('keys.detail.statusMessages.active')
                  : keyData.status === 'FAILED'
                  ? t('keys.detail.statusMessages.failed')
                  : t('keys.detail.statusMessages.pending')
                }
              </p>
            </div>
          </div>
        </div>

        {/* Información Básica */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <Shield size={18} className="text-blue-500" />
            {t('keys.detail.basicInfo')}
          </h4>
          <div className="space-y-0">
            <DetailRow label={t('keys.detail.name')} value={keyData.name} />
            <DetailRow
              label={keyData.ctmKeyId ? t('keys.detail.ctmKeyId') : t('keys.detail.localId')}
              value={keyData.ctmKeyId || keyData.id}
            />
            {keyData.ctmKeyId && (
              <DetailRow label={t('keys.detail.localId')} value={keyData.id} />
            )}
            <DetailRow
              label={t('keys.detail.typeAlgorithm')}
              value={keyData.algorithm || keyData.type || 'N/A'}
            />
            <DetailRow label={t('keys.detail.numBytes')} value={keyData.numBytes || keyData.num_bytes || 'N/A'} />
            <DetailRow label={t('keys.detail.exportable')} value={keyData.exportable ? t('keys.detail.yes') : t('keys.detail.no')} />
          </div>
        </div>

        {/* Reporte de Auto-certificación - RGB VERDE MINIMALISTA ✨ */}
        {keyData.entropyReport && (
          <div className={`p-4 rounded-xl shadow-sm relative overflow-hidden ${
            darkMode
              ? 'bg-gray-800/50 backdrop-blur-sm'
              : 'bg-white/80 backdrop-blur-sm'
          }`}
          style={{
            border: '2px solid transparent',
            background: darkMode
              ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(21, 128, 61, 0.05), rgba(15, 78, 46, 0.1)) padding-box, linear-gradient(135deg, rgba(34, 197, 94, 0.4), rgba(21, 128, 61, 0.3), rgba(15, 78, 46, 0.4)) border-box'
              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9)) padding-box, linear-gradient(135deg, rgba(34, 197, 94, 0.6), rgba(16, 185, 129, 0.5), rgba(5, 150, 105, 0.6)) border-box',
            boxShadow: darkMode
              ? '0 4px 20px rgba(34, 197, 94, 0.1)'
              : '0 4px 25px rgba(34, 197, 94, 0.25), 0 0 20px rgba(16, 185, 129, 0.15)'
          }}>
            <h4 className="text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-gray-100">
              <Shield size={18} className="text-green-500" />
              {t('keys.detail.selfCertReport')}
            </h4>
            <div className="space-y-0">
              {(() => {
                const entropyData = parseEntropyReport(keyData.entropyReport);
                if (!entropyData) {
                  return (
                    <DetailRow label="Datos" value="Error al parsear el reporte" />
                  );
                }
                return (
                  <>
                    {entropyData.entropy_status && (
                      <DetailRow
                        label={t('keys.detail.entropyStatus')}
                        value={entropyData.entropy_status}
                      />
                    )}
                    {entropyData.source && (
                      <DetailRow
                        label={t('keys.detail.source')}
                        value={entropyData.source}
                      />
                    )}
                    {entropyData.quantum_fidelity && (
                      <DetailRow
                        label={t('keys.detail.quantumFidelity')}
                        value={entropyData.quantum_fidelity}
                      />
                    )}
                    {entropyData.error_string && (
                      <DetailRow
                        label={t('keys.detail.quantumFidelity')}
                        value={entropyData.error_string}
                      />
                    )}
                  </>
                );
              })()}
            </div>
          </div>
        )}

        {/* Fechas */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <Calendar size={18} className="text-blue-500" />
            {t('keys.detail.dates')}
          </h4>
          <div className="space-y-0">
            <DetailRow label={t('keys.detail.created')} value={formatDate(keyData.createdAt)} />
            <DetailRow label={t('keys.detail.updated')} value={formatDate(keyData.updatedAt)} />
            {keyData.uploadedAt && (
              <DetailRow label={t('keys.detail.uploadedToCTM')} value={formatDate(keyData.uploadedAt)} />
            )}
          </div>
        </div>

        {/* MATERIAL DE LA LLAVE - COMPACTO 🔐 */}
        {keyData.key_material_base64 && (
          <div className={`p-4 rounded-xl border shadow-md ${
            darkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'
          }`}>
            <h4 className="text-base font-semibold mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
              <Key size={18} className="text-gray-500" />
              {t('keys.detail.keyMaterial')}
            </h4>
            <div className="space-y-1">
              <DetailRow
                label={t('keys.detail.base64')}
                value={keyData.key_material_base64.substring(0, 50) + '...'}
                copyable
              />
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 flex items-center gap-1">
              <span>⚠️</span> {t('keys.detail.sensitiveInfo')}
            </p>
          </div>
        )}

        {/* INFORMACIÓN TÉCNICA - COMPACTA 🔧 */}
        {keyData.error_message && (
          <div className={`p-4 rounded-xl border shadow-md ${
            darkMode ? 'bg-red-900/10 border-red-700' : 'bg-red-50 border-red-200'
          }`}>
            <h4 className="text-base font-semibold mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
              <div className="p-1.5 rounded-lg bg-red-500 shadow-sm">
                <AlertCircle size={16} className="text-white" />
              </div>
              {t('keys.detail.technicalInfo')}
            </h4>
            <div className="space-y-1">
              <DetailRow label={t('keys.detail.errorMessage')} value={keyData.error_message} />
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="flex justify-center mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={onClose}
          className="px-8 py-3 rounded-xl bg-blue-600 hover:bg-blue-700 text-white font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
        >
          {getTranslation('closeButton')}
        </button>
      </div>
    </Modal>
  );
};

KeyDetailModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  keyData: PropTypes.object,
  darkMode: PropTypes.bool.isRequired,
  isAdmin: PropTypes.bool
};

export default KeyDetailModal;
