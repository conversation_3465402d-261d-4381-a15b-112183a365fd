import PropTypes from 'prop-types';

/**
 * Componente ErrorAlert reutilizable
 * Muestra mensajes de error de manera consistente
 */
const ErrorAlert = ({ 
  error, 
  onClose,
  className = ""
}) => {
  if (!error) return null;

  return (
    <div className={`mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg ${className}`}>
      Error: {error}
      {onClose && (
        <button
          onClick={onClose}
          className="ml-2 text-red-500 hover:text-red-700"
          aria-label="Cerrar error"
        >
          ✕
        </button>
      )}
    </div>
  );
};

ErrorAlert.propTypes = {
  error: PropTypes.string,
  onClose: PropTypes.func,
  className: PropTypes.string
};

export default ErrorAlert;
