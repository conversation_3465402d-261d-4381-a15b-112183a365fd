import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { CheckCircle, X } from 'lucide-react';

const SuccessModal = ({ 
  isOpen, 
  onClose, 
  title = "¡Éxito!", 
  message = "Operación completada exitosamente",
  autoCloseDelay = 2500,
  darkMode = false 
}) => {
  useEffect(() => {
    if (isOpen && autoCloseDelay > 0) {
      const timer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);

      return () => clearTimeout(timer);
    }
  }, [isOpen, autoCloseDelay, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className={`relative transform transition-all duration-300 scale-100 opacity-100 animate-bounce-in ${
        darkMode ? 'text-white' : 'text-gray-900'
      }`}>
        <div className={`relative mx-4 rounded-2xl shadow-2xl border-2 p-8 max-w-md w-full ${
          darkMode 
            ? 'bg-gray-800 border-gray-600' 
            : 'bg-white border-gray-200'
        }`}>
          
          {/* Botón de cerrar */}
          <button
            onClick={onClose}
            className={`absolute top-4 right-4 p-2 rounded-full transition-colors duration-200 ${
              darkMode
                ? 'hover:bg-gray-700 text-gray-400 hover:text-white'
                : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
            }`}
          >
            <X size={20} />
          </button>

          {/* Contenido */}
          <div className="text-center">
            {/* Icono de éxito con animación */}
            <div className="mx-auto mb-6 w-20 h-20 relative">
              <div className="absolute inset-0 bg-green-100 dark:bg-green-900/30 rounded-full animate-pulse"></div>
              <div className="relative flex items-center justify-center w-full h-full">
                <CheckCircle 
                  size={48} 
                  className="text-green-500 animate-scale-in" 
                />
              </div>
            </div>

            {/* Título */}
            <h3 className="text-2xl font-light tracking-wide mb-4 text-green-600 dark:text-green-400">
              {title}
            </h3>

            {/* Mensaje */}
            <p className={`text-lg font-light tracking-wide leading-relaxed ${
              darkMode ? 'text-gray-300' : 'text-gray-600'
            }`}>
              {message}
            </p>

            {/* Barra de progreso para auto-close */}
            {autoCloseDelay > 0 && (
              <div className="mt-6">
                <div className={`w-full h-1 rounded-full overflow-hidden ${
                  darkMode ? 'bg-gray-700' : 'bg-gray-200'
                }`}>
                  <div 
                    className="h-full bg-gradient-to-r from-green-500 to-green-600 rounded-full animate-progress"
                    style={{
                      animation: `progress ${autoCloseDelay}ms linear forwards`
                    }}
                  />
                </div>
                <p className={`text-xs mt-2 ${
                  darkMode ? 'text-gray-400' : 'text-gray-500'
                }`}>
                  Se cerrará automáticamente...
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Estilos CSS personalizados */}
      <style jsx>{`
        @keyframes bounce-in {
          0% {
            transform: scale(0.3) translateY(-50px);
            opacity: 0;
          }
          50% {
            transform: scale(1.05);
          }
          70% {
            transform: scale(0.9);
          }
          100% {
            transform: scale(1) translateY(0);
            opacity: 1;
          }
        }

        @keyframes scale-in {
          0% {
            transform: scale(0);
            opacity: 0;
          }
          50% {
            transform: scale(1.2);
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }

        @keyframes progress {
          0% {
            width: 0%;
          }
          100% {
            width: 100%;
          }
        }

        .animate-bounce-in {
          animation: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .animate-scale-in {
          animation: scale-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.2s both;
        }

        .animate-progress {
          animation: progress linear forwards;
        }
      `}</style>
    </div>
  );
};

SuccessModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string,
  message: PropTypes.string,
  autoCloseDelay: PropTypes.number,
  darkMode: PropTypes.bool
};

export default SuccessModal;
