{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "close": "Close", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "clear": "Clear", "refresh": "Refresh", "view": "View", "download": "Download", "upload": "Upload", "create": "Create", "update": "Update", "remove": "Remove", "yes": "Yes", "no": "No"}, "navigation": {"dashboard": "Dashboard", "keys": "My Keys", "profile": "My Profile", "settings": "Settings", "logout": "Logout", "darkMode": "Dark Mode", "lightMode": "Light Mode"}, "dashboard": {"welcome": "Welcome, {{name}}", "subtitle": "Here you can manage your quantum keys and update your personal information.", "stats": {"myKeys": "My Keys", "activeKeys": "Active Keys", "totalKeys": "Total Keys", "pendingKeys": "Pending Keys", "lastAccess": "Last Access", "keysGenerated": "Quantum keys generated.", "keysActive": "Keys in operation.", "keysPending": "Keys pending upload.", "lastLoginDate": "Last login date."}, "miniStats": {"successful": "Successful", "failed": "Failed", "inCTM": "In CTM", "successRate": "% Success"}}, "admin": {"welcome": "Welcome, Quantum Administrator", "subtitle": "QRNG Quantum System - Administration Panel", "navigation": {"users": "Users", "keys": "Keys"}, "profile": {"title": "My Administrator Profile", "subtitle": "Manage your personal information and account settings as administrator"}, "keys": {"title": "Quantum Keys Management", "subtitle": "Manage and monitor all quantum keys in the QRNG system", "description": "From here you can monitor the status of all quantum keys generated by users, review usage statistics, and manage global system key configurations.", "features": {"monitoring": {"title": "Real-Time Monitoring", "description": "Monitor the status and performance of all active quantum keys"}, "analytics": {"title": "Analytics & Statistics", "description": "Get detailed insights about system usage and efficiency"}, "management": {"title": "Centralized Management", "description": "Manage global configurations and security policies"}}, "comingSoon": "Coming Soon", "developmentNote": "This section is under active development and will be available in the next system update."}, "stats": {"totalUsers": "Total Users", "totalUsersDesc": "Users registered in the system.", "activeKeys": "Active Keys", "activeKeysDesc": "<PERSON> successfully uploaded to CTM.", "activeUsers": "Active Users", "activeUsersDesc": "Users with active status.", "totalKeys": "Total Keys", "successfulKeys": "Successful", "failedKeys": "Failed", "inCTM": "In CTM", "total": "Total", "active": "Active", "failed": "Failed", "pending": "Pending", "usersWithKeys": "Users with keys", "searchPlaceholder": "Search keys, users...", "allUsers": "All users", "allStatuses": "All statuses", "allAlgorithms": "All algorithms", "clearFilters": "Clear filters", "systemKeys": "System Keys", "noKeysFound": "No keys found", "adjustFilters": "Try adjusting the search filters", "userSummary": "User Summary", "viewKeyDetail": "View key detail", "viewUserKeys": "View all user keys", "viewUserKeysTitle": "View user keys", "user": "User", "algorithm": "Algorithm", "bytes": "Bytes", "created": "Created", "noName": "No name"}, "users": {"title": "User Management", "subtitle": "Manage users of the QRNG Quantum system", "createUser": "Add User", "active": "Active", "inactive": "Inactive", "admin": "Admin", "user": "User", "viewKeys": "View user keys", "editUser": "Edit user", "viewDetail": "View Detail", "deleteUser": "Delete user", "keyStats": "Key Statistics", "create": {"title": "Create New User", "personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "password": "Password", "company": "Company", "role": "Role", "roles": {"user": "User", "admin": "Administrator"}, "passwordRequirements": "Password Requirements", "minLength": "Minimum 8 characters", "alphanumeric": "Alphanumeric characters and symbols allowed", "serviceConfig": "Service Configuration", "ctm": {"title": "CipherTrust Manager", "ipAddress": "IP Address", "username": "Username", "password": "Password", "domain": "Domain"}, "seqrng": {"title": "SEQRNG", "ipAddress": "IP Address", "apiToken": "API Token"}, "creating": "Creating User...", "createButton": "Create User", "validation": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email is required", "passwordRequired": "Password is required"}}, "delete": {"title": "Delete User", "confirmQuestion": "Delete User?", "confirmMessage": "Are you sure you want to delete the user", "warningMessage": "This action cannot be undone", "deleteButton": "Delete User"}, "edit": {"title": "Edit User", "personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "company": "Company", "role": "Role", "status": "Status", "serviceConfig": "Service Configuration", "saveButton": "Save Changes", "saving": "Saving Changes...", "roles": {"user": "User", "admin": "Administrator"}, "statusOptions": {"active": "Active", "inactive": "Inactive"}, "ctm": {"title": "CipherTrust Manager", "ipAddress": "IP Address", "username": "Username", "password": "Password", "domain": "Domain"}, "seqrng": {"title": "SEQRNG", "ipAddress": "IP Address", "apiToken": "API Token"}}, "keys": {"title": "User Keys", "totalKeys": "Total keys", "active": "Active", "failed": "Failed", "viewDetail": "View Detail", "close": "Close", "loading": "Loading user keys...", "statusActive": "Active in CTM", "statusFailed": "Failed", "statusPending": "Pending", "detail": {"title": "Key Details", "closeButton": "Close"}}, "detail": {"title": "User Details", "personalInfo": "Personal Information", "fullName": "Full Name", "email": "Email", "company": "Company", "role": "Role", "status": "Status", "administrator": "Administrator", "user": "User", "active": "Active", "inactive": "Inactive", "cipherTrustManager": "CipherTrust Manager", "seqrng": "SEQRNG", "ipAddress": "IP Address", "username": "Username", "password": "Password", "domain": "Domain", "apiToken": "API Token"}}}, "keys": {"title": "My Quantum Keys", "subtitle": "Manage your quantum keys, view details and upload new keys to the CTM system.", "uploadNew": "Upload New Key", "noKeys": "You have no registered keys. Create your first key!", "status": {"active": "Active", "pending": "Pending", "failed": "Failed", "uploaded": "Uploaded to CTM", "uploaded_to_ctm": "Uploaded to CTM"}, "details": {"name": "Name", "algorithm": "Algorithm", "created": "Created", "ctmKeyId": "CTM Key ID", "localId": "Local ID", "type": "Type", "status": "Status"}, "actions": {"viewDetail": "View key details", "deleteKey": "Delete key", "uploadToCTM": "Upload to CTM"}, "upload": {"title": "Upload New Key to CTM", "subtitle": "Generate a new secure quantum key", "keyInfo": "Key Information", "technicalConfig": "Technical Configuration", "algorithmLimits": "Algorithm Limits", "keyName": "Key Name", "keyNamePlaceholder": "my-encryption-key-001", "keyNameHelp": "Only letters, numbers, hyphens and underscores", "algorithm": "Algorithm", "keySize": "Number of Bytes", "keySizeHelp": "Between 1 and 1024 bytes", "maxBytes": "Max. {{bytes}} bytes", "maxPermitted": "Maximum permitted for {{algorithm}}: {{bytes}} bytes", "exportable": "Exportable", "maxBytesShort": "Max. {{bytes}} bytes", "errorMaxBytes": "{{algorithm}} allows maximum {{bytes}} bytes", "allowedValues": "Allowed values: {{values}} bytes", "selectAllowedValue": "Select one of the allowed values for {{algorithm}}", "autoGeneration": "Automatic Key Generation", "autoGenerationDesc": "Cryptographic material will be automatically generated securely using quantum algorithms.", "uploading": "Uploading...", "uploadButton": "Upload to CTM"}, "detail": {"title": "Key Details", "status": "Status", "statusMessages": {"active": "The key is active in the system", "failed": "Error during processing", "pending": "Processing and validating"}, "basicInfo": "Basic Information", "name": "Name", "ctmKeyId": "CTM Key ID", "localId": "Local ID", "typeAlgorithm": "Type/Algorithm", "numBytes": "Number of Bytes", "exportable": "Exportable", "yes": "Yes", "no": "No", "selfCertReport": "Self-certification Report", "entropyStatus": "Entropy Status", "source": "Source", "quantumFidelity": "Quantum Fidelity", "dates": "Dates", "created": "Created", "updated": "Updated", "uploadedToCTM": "Uploaded to CTM", "keyMaterial": "Key Material", "base64": "Base64", "sensitiveInfo": "Sensitive information", "technicalInfo": "Technical Information", "errorMessage": "Error Message", "closeButton": "Close", "copyTooltip": "Copy"}, "delete": {"title": "Confirm Deletion", "subtitle": "This action is irreversible", "confirmQuestion": "Are you sure you want to delete the key?", "warningMessage": "This action cannot be undone. The key will be permanently deleted from the system.", "deleteButton": "Delete Key"}}, "profile": {"title": "My Profile", "subtitle": "Manage your personal information and account settings", "changePassword": "Change Password", "editProfile": "Edit Profile", "fullName": "Full Name", "email": "Email", "company": "Company", "registrationDate": "Registration Date", "notSpecified": "Not specified", "loadingProfile": "Loading profile information...", "changePasswordModal": {"title": "Change Password", "subtitle": "Update your password to keep your account secure", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "currentPasswordPlaceholder": "Enter your current password", "newPasswordPlaceholder": "Enter your new password", "confirmPasswordPlaceholder": "Confirm your new password", "requirements": "Password Requirements", "requirementsList": {"minLength": "Minimum 8 characters", "alphanumeric": "Alphanumeric characters and symbols allowed", "different": "Must be different from current password"}, "changeButton": "Change Password", "changingButton": "Changing...", "errors": {"currentRequired": "Current password is required", "confirmRequired": "Must confirm the new password", "passwordsNotMatch": "Passwords do not match", "samePassword": "New password must be different from current password"}}, "editProfileModal": {"title": "Edit My Profile", "subtitle": "Update your personal information to keep your profile up to date", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "serviceConfig": "Service Configuration", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "company": "Company (optional)", "firstNamePlaceholder": "Enter your first name", "lastNamePlaceholder": "Enter your last name", "emailPlaceholder": "Enter your email", "companyPlaceholder": "Enter your company", "serviceConfigNote": "CipherTrust Manager and SEQRNG configuration is managed by the system administrator. If you need to modify these settings, contact your administrator.", "selectAvatar": "Select Avatar", "currentAvatar": "Current avatar", "selectAvatarDescription": "Select a new avatar from the available options", "saveButton": "Save Changes", "savingButton": "Saving...", "updateError": "Error updating profile"}, "personalInfo": "Personal Information", "contactInfo": "Contact Information", "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "company": "Company", "registrationDate": "Registration Date"}, "placeholders": {"firstName": "Enter your first name", "lastName": "Enter your last name", "email": "Enter your email", "company": "Enter your company"}, "edit": {"title": "Edit My Profile", "subtitle": "Update your personal information to keep your profile up to date", "saveChanges": "Save Changes", "saving": "Saving..."}, "password": {"title": "Change Password", "subtitle": "Update your password to keep your account secure", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "requirements": "Password Requirements", "minLength": "Minimum 8 characters", "alphanumeric": "Alphanumeric characters and symbols allowed", "different": "Must be different from current password", "changing": "Changing...", "change": "Change Password"}}, "language": {"selector": "Language", "spanish": "Español", "english": "English"}, "errors": {"generic": "An unexpected error occurred", "network": "Connection error", "unauthorized": "Unauthorized", "forbidden": "Access denied", "notFound": "Not found", "validation": "Validation error", "required": "This field is required", "invalidEmail": "Invalid email", "passwordMismatch": "Passwords do not match", "passwordTooShort": "Password must be at least 8 characters"}, "messages": {"profileUpdated": "Profile updated successfully", "passwordChanged": "Password changed successfully", "keyUploaded": "Key uploaded successfully", "keyDeleted": "Key deleted successfully", "confirmLogout": "Logout?", "confirmLogoutSubtitle": "This action will close your current session", "confirmLogoutQuestion": "Are you sure you want to close your session?", "confirmLogoutWarning": "You will need to log in again to access the system.", "confirmDelete": "Are you sure you want to delete this key?"}}