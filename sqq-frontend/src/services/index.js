/**
 * Services Index
 * Punto de entrada centralizado para todos los servicios
 */

// Core services
export { default as httpClient, HttpError } from './core/httpClient.js';

// Configuration
export { API_CONFIG, API_ENDPOINTS } from './config/apiConfig.js';

// Auth services
export { default as authService } from './auth/authService.js';

// User services
export { default as userService } from './users/userService.js';

// Key services
export { default as keyService } from './keys/keyService.js';

// Convenience exports for common operations
export const api = {
  auth: {
    login: (email, password) => import('./auth/authService.js').then(m => m.default.login(email, password)),
    logout: () => import('./auth/authService.js').then(m => m.default.logout()),
    getProfile: () => import('./auth/authService.js').then(m => m.default.getProfile()),
    refreshToken: (token) => import('./auth/authService.js').then(m => m.default.refreshToken(token)),
    isTokenValid: () => import('./auth/authService.js').then(m => m.default.isTokenValid()),
    getUserRole: () => import('./auth/authService.js').then(m => m.default.getUserRole()),
  },
  
  users: {
    getAll: () => import('./users/userService.js').then(m => m.default.getAllUsers()),
    create: (userData) => import('./users/userService.js').then(m => m.default.createUser(userData)),
    update: (id, data) => import('./users/userService.js').then(m => m.default.updateUser(id, data)),
    getById: (id) => import('./users/userService.js').then(m => m.default.getUserById(id)),
  },
  
  keys: {
    uploadToCTM: (keyData) => import('./keys/keyService.js').then(m => m.default.uploadToCTM(keyData)),
    getByUser: (userId, filters) => import('./keys/keyService.js').then(m => m.default.getKeysByUser(userId, filters)),
    getStatistics: (userId) => import('./keys/keyService.js').then(m => m.default.getKeyStatistics(userId)),
  },
};
