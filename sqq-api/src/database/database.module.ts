import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { User } from '../users/entities/user.entity';
import { Key } from '../keys/entities/key.entity';
import { SeedersModule } from './seeders/seeders.module';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('database.host'),
        port: configService.get('database.port'),
        username: configService.get('database.username'),
        password: configService.get('database.password'),
        database: configService.get('database.database'),
        entities: [User, Key],
        synchronize: process.env.NODE_ENV === 'development', // Solo en desarrollo
        logging: process.env.NODE_ENV === 'development',
        ssl: false,
      }),
      inject: [ConfigService],
    }),
    SeedersModule,
  ],
})
export class DatabaseModule {}
