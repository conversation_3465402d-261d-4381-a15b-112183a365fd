import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import * as bcrypt from 'bcryptjs';

export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  email: string;

  @Column({ type: 'varchar', length: 100 })
  firstName: string;

  @Column({ type: 'varchar', length: 100 })
  lastName: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  company: string;

  @Column({ type: 'text' })
  @Exclude()
  password: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  role: UserRole;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'text', nullable: true })
  @Exclude()
  refreshToken: string | null;

  // CTM Configuration fields
  @Column({ type: 'varchar', length: 255, nullable: true })
  ctmIpAddress: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  ctmUsername: string | null;

  @Column({ type: 'text', nullable: true })
  @Exclude()
  ctmPassword: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  ctmDomain: string | null;

  // SeqRNG Configuration fields
  @Column({ type: 'varchar', length: 255, nullable: true })
  seqrngIpAddress: string | null;

  @Column({ type: 'text', nullable: true })
  @Exclude()
  seqrngApiToken: string | null;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password) {
      const salt = await bcrypt.genSalt(12);
      this.password = await bcrypt.hash(this.password, salt);
    }

    // Note: CTM password and SeqRNG token encryption is handled in the service layer
    // to avoid circular dependency with EncryptionService
  }

  async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password);
  }

  // Note: CTM password and SeqRNG token validation is handled in the service layer
  // since they use encryption instead of hashing

  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  get hasCtmConfiguration(): boolean {
    return !!(
      this.ctmIpAddress &&
      this.ctmUsername &&
      this.ctmPassword &&
      this.ctmDomain
    );
  }

  get hasSeqrngConfiguration(): boolean {
    return !!(
      this.seqrngIpAddress &&
      this.seqrngApiToken
    );
  }
}
